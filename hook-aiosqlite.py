# Hook for aiosqlite
from PyInstaller.utils.hooks import collect_all, collect_submodules

# Collect all aiosqlite modules and data
datas, binaries, hiddenimports = collect_all('aiosqlite')

# Also collect submodules explicitly
hiddenimports += collect_submodules('aiosqlite')

# Add specific modules that might be missed
hiddenimports += [
    'aiosqlite.core',
    'aiosqlite.context',
    'aiosqlite.cursor',
    'sqlite3'
]
