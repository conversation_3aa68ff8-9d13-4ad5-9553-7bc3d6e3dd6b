2025-07-05 17:31:10,613 - __main__ - INFO - Bot process lock acquired successfully.
2025-07-05 17:31:10,614 - __main__ - INFO - Initializing database...
2025-07-05 17:31:10,939 - event_monitor - INFO - Database initialized with clean schema. Rebuild: False
2025-07-05 17:31:10,975 - __main__ - INFO - Building application...
2025-07-05 17:31:11,932 - event_monitor - INFO - Setting up event monitor handlers...
2025-07-05 17:31:11,933 - event_monitor - INFO - Event monitor handlers setup complete.
2025-07-05 17:31:11,934 - __main__ - INFO - Starting bot polling...
2025-07-05 17:31:12,166 - __main__ - WARNING - Signal handlers SIGINT/SIGTERM not fully supported on Windows asyncio loop.
2025-07-05 17:31:12,167 - __main__ - INFO - Use Ctrl+C to trigger KeyboardInterrupt for shutdown.
2025-07-05 17:31:12,169 - __main__ - INFO - Basic SIGINT handler set using signal.signal (may have limitations).
2025-07-05 17:31:12,169 - __main__ - INFO - Signal handler setup complete.
2025-07-05 17:31:12,170 - telegram.ext.Application - INFO - Application started
2025-07-05 17:31:12,183 - event_monitor - INFO - Bot commands updated successfully.
2025-07-05 17:31:12,241 - __main__ - INFO - Monitor Bot started successfully!
2025-07-05 17:31:29,117 - event_monitor - INFO - Monitor 3c5d60bcb0df saved/updated in database.
2025-07-05 17:31:29,148 - event_monitor - INFO - Started monitoring task for amr-diab-jeddah-concert-2025-10-jul-tickets-515709 (ID: 3c5d60bcb0df)
2025-07-05 17:31:29,149 - event_monitor - INFO - Starting monitor task for amr-diab-jeddah-concert-2025-10-jul-tickets-515709 (ID: 3c5d60bcb0df)
2025-07-05 17:31:29,639 - event_monitor - INFO - Channel keys updated for monitor 3c5d60bcb0df
2025-07-05 17:31:29,660 - event_monitor - INFO - Initial WeBook key structure stored for amr-diab-jeddah-concert-2025-10-jul-tickets-515709
2025-07-05 17:31:30,176 - event_monitor - WARNING - Error checking Seats.io event amr-diab-jeddah-season-10-jul: SOCKSHTTPSConnectionPool(host='cdn-eu.seatsio.net', port=443): Max retries exceeded with url: /system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/rendering-info?event_key=amr-diab-jeddah-season-10-jul (Caused by NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x000001C81BFC75C0>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data'))
2025-07-05 17:31:30,272 - event_monitor - INFO - Error recorded for monitor 3c5d60bcb0df (Target: seatsio, Type: ConnectionError)
2025-07-05 17:31:30,343 - webook_pro - INFO - Successfully fetched chart token: 5209d4d8...
2025-07-05 17:31:30,344 - webook_pro - INFO - Chart token refreshed: 5209d4d8...
2025-07-05 17:31:45,019 - event_monitor - WARNING - Error checking Seats.io event amr-diab-jeddah-season-10-jul: SOCKSHTTPSConnectionPool(host='cdn-eu.seatsio.net', port=443): Max retries exceeded with url: /system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/rendering-info?event_key=amr-diab-jeddah-season-10-jul (Caused by NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x000001C81C001790>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data'))
2025-07-05 17:31:45,128 - event_monitor - INFO - Error recorded for monitor 3c5d60bcb0df (Target: seatsio, Type: ConnectionError)
2025-07-05 17:38:04,406 - __main__ - CRITICAL - FATAL: Could not acquire lock. Another bot instance may be running.
2025-07-05 17:38:04,407 - __main__ - CRITICAL - Exiting to prevent multiple instances conflict.
2025-07-05 17:38:04,411 - __main__ - INFO - System exit called with code: 1
2025-07-05 17:38:04,412 - __main__ - INFO - Application exiting.
2025-07-05 17:38:04,412 - __main__ - INFO - Bot process lock released.
2025-07-05 17:38:12,614 - __main__ - INFO - Bot process lock acquired successfully.
2025-07-05 17:38:12,616 - __main__ - INFO - Initializing database...
2025-07-05 17:38:12,646 - event_monitor - INFO - Database initialized with clean schema. Rebuild: False
2025-07-05 17:38:12,648 - __main__ - INFO - Building application...
2025-07-05 17:38:13,763 - event_monitor - INFO - Setting up event monitor handlers...
2025-07-05 17:38:13,765 - event_monitor - INFO - Event monitor handlers setup complete.
2025-07-05 17:38:13,766 - __main__ - INFO - Starting bot polling...
2025-07-05 17:38:14,076 - __main__ - WARNING - Signal handlers SIGINT/SIGTERM not fully supported on Windows asyncio loop.
2025-07-05 17:38:14,077 - __main__ - INFO - Use Ctrl+C to trigger KeyboardInterrupt for shutdown.
2025-07-05 17:38:14,078 - __main__ - INFO - Basic SIGINT handler set using signal.signal (may have limitations).
2025-07-05 17:38:14,078 - __main__ - INFO - Signal handler setup complete.
2025-07-05 17:38:14,079 - telegram.ext.Application - INFO - Application started
2025-07-05 17:38:14,100 - event_monitor - INFO - Bot commands updated successfully.
2025-07-05 17:38:14,155 - __main__ - INFO - Monitor Bot started successfully!
2025-07-05 17:38:19,884 - event_monitor - INFO - Monitor 267ba0c2117e saved/updated in database.
2025-07-05 17:38:19,909 - event_monitor - INFO - Started monitoring task for ewc-concerts-tamer-hosny-11-jul-tickets-836325 (ID: 267ba0c2117e)
2025-07-05 17:38:19,909 - event_monitor - INFO - Starting monitor task for ewc-concerts-tamer-hosny-11-jul-tickets-836325 (ID: 267ba0c2117e)
2025-07-05 17:38:20,385 - event_monitor - INFO - Channel keys updated for monitor 267ba0c2117e
2025-07-05 17:38:20,410 - event_monitor - INFO - Initial WeBook key structure stored for ewc-concerts-tamer-hosny-11-jul-tickets-836325
2025-07-05 17:38:22,788 - event_monitor - INFO - Initial Seats.io response hash stored for tamer-hosny-ewc-concerts-11-jul
2025-08-31 16:10:21,027 - __main__ - CRITICAL - FATAL: Could not acquire lock. Another bot instance may be running.
2025-08-31 16:10:21,028 - __main__ - CRITICAL - Exiting to prevent multiple instances conflict.
2025-08-31 16:10:21,033 - __main__ - INFO - System exit called with code: 1
2025-08-31 16:10:21,034 - __main__ - INFO - Application exiting.
2025-08-31 16:10:21,034 - __main__ - INFO - Bot process lock released.
2025-08-31 16:10:33,380 - __main__ - INFO - Bot process lock acquired successfully.
2025-08-31 16:10:33,380 - __main__ - INFO - Initializing database...
2025-08-31 16:10:33,414 - event_monitor - INFO - Database initialized with clean schema. Rebuild: False
2025-08-31 16:10:33,415 - __main__ - INFO - Building application...
2025-08-31 16:10:34,513 - event_monitor - INFO - Setting up event monitor handlers...
2025-08-31 16:10:34,514 - event_monitor - INFO - Event monitor handlers setup complete.
2025-08-31 16:10:34,514 - __main__ - INFO - Starting bot polling...
2025-08-31 16:10:34,781 - __main__ - WARNING - Signal handlers SIGINT/SIGTERM not fully supported on Windows asyncio loop.
2025-08-31 16:10:34,781 - __main__ - INFO - Use Ctrl+C to trigger KeyboardInterrupt for shutdown.
2025-08-31 16:10:34,783 - __main__ - INFO - Basic SIGINT handler set using signal.signal (may have limitations).
2025-08-31 16:10:34,784 - __main__ - INFO - Signal handler setup complete.
2025-08-31 16:10:34,785 - telegram.ext.Application - INFO - Application started
2025-08-31 16:10:34,800 - event_monitor - INFO - Bot commands updated successfully.
2025-08-31 16:10:34,855 - __main__ - INFO - Monitor Bot started successfully!
2025-08-31 16:10:35,082 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\ext\_extbot.py", line 658, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\_bot.py", line 4593, in get_updates
    await self._post(
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\_bot.py", line 691, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\ext\_extbot.py", line 362, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\_bot.py", line 720, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-09-09 23:12:59,452 - __main__ - INFO - Bot process lock acquired successfully.
2025-09-09 23:12:59,471 - __main__ - INFO - Initializing database...
2025-09-09 23:12:59,490 - event_monitor - INFO - Database initialized with clean schema. Rebuild: False
2025-09-09 23:12:59,492 - __main__ - INFO - Building application...
2025-09-09 23:13:00,294 - event_monitor - INFO - Setting up event monitor handlers...
2025-09-09 23:13:00,295 - event_monitor - INFO - Event monitor handlers setup complete.
2025-09-09 23:13:00,295 - __main__ - INFO - Starting bot polling...
2025-09-09 23:13:00,595 - __main__ - WARNING - Signal handlers SIGINT/SIGTERM not fully supported on Windows asyncio loop.
2025-09-09 23:13:00,595 - __main__ - INFO - Use Ctrl+C to trigger KeyboardInterrupt for shutdown.
2025-09-09 23:13:00,596 - __main__ - INFO - Basic SIGINT handler set using signal.signal (may have limitations).
2025-09-09 23:13:00,596 - __main__ - INFO - Signal handler setup complete.
2025-09-09 23:13:00,596 - telegram.ext.Application - INFO - Application started
2025-09-09 23:13:00,612 - event_monitor - INFO - Bot commands updated successfully.
2025-09-09 23:13:00,667 - __main__ - INFO - Monitor Bot started successfully!
2025-09-09 23:13:05,056 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\ext\_extbot.py", line 658, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\_bot.py", line 4593, in get_updates
    await self._post(
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\_bot.py", line 691, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\ext\_extbot.py", line 362, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\_bot.py", line 720, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\Programming\Python\webook\batches_notifications\webook_batches_notifications - Copy\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-09-09 23:13:06,768 - __main__ - INFO - Received signal 2. Initiating shutdown...
2025-09-09 23:13:06,769 - __main__ - INFO - Initiating graceful shutdown...
2025-09-09 23:13:06,770 - __main__ - INFO - Cancelling 0 active monitoring tasks...
2025-09-09 23:13:06,771 - __main__ - INFO - Stopping application components...
2025-09-09 23:13:07,702 - __main__ - INFO - Main loop exiting after shutdown.
2025-09-09 23:13:07,706 - __main__ - INFO - Application exiting.
2025-09-09 23:13:07,707 - __main__ - INFO - Bot process lock released.
2025-09-09 23:13:59,737 - __main__ - INFO - Bot process lock acquired successfully.
2025-09-09 23:13:59,738 - __main__ - INFO - Initializing database...
2025-09-09 23:13:59,742 - event_monitor - INFO - Database initialized with clean schema. Rebuild: False
2025-09-09 23:13:59,744 - __main__ - INFO - Building application...
2025-09-09 23:14:00,986 - event_monitor - INFO - Setting up event monitor handlers...
2025-09-09 23:14:00,987 - event_monitor - INFO - Event monitor handlers setup complete.
2025-09-09 23:14:00,987 - __main__ - INFO - Starting bot polling...
2025-09-09 23:14:01,216 - __main__ - WARNING - Signal handlers SIGINT/SIGTERM not fully supported on Windows asyncio loop.
2025-09-09 23:14:01,216 - __main__ - INFO - Use Ctrl+C to trigger KeyboardInterrupt for shutdown.
2025-09-09 23:14:01,217 - __main__ - INFO - Basic SIGINT handler set using signal.signal (may have limitations).
2025-09-09 23:14:01,218 - __main__ - INFO - Signal handler setup complete.
2025-09-09 23:14:01,219 - telegram.ext.Application - INFO - Application started
2025-09-09 23:14:01,234 - event_monitor - INFO - Bot commands updated successfully.
2025-09-09 23:14:01,288 - __main__ - INFO - Monitor Bot started successfully!
2025-09-09 23:14:19,418 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:14:19,418 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:14:39,447 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:14:39,448 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:14:59,476 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:14:59,476 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:15:04,235 - event_monitor - INFO - Admin 5054080482 initiated channel verification with code VERIFY-98DE7A318A
2025-09-09 23:15:19,504 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:15:19,505 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:15:23,160 - __main__ - INFO - Received signal 2. Initiating shutdown...
2025-09-09 23:15:23,161 - __main__ - INFO - Initiating graceful shutdown...
2025-09-09 23:15:23,165 - __main__ - INFO - Cancelling 0 active monitoring tasks...
2025-09-09 23:15:23,165 - __main__ - INFO - Stopping application components...
2025-09-09 23:15:23,405 - __main__ - INFO - Updater stopped.
2025-09-09 23:15:23,406 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-09-09 23:15:23,406 - telegram.ext.Application - INFO - Application.stop() complete
2025-09-09 23:15:23,407 - __main__ - INFO - Application stopped.
2025-09-09 23:15:23,408 - __main__ - INFO - Application shutdown complete.
2025-09-09 23:15:23,410 - __main__ - INFO - Bot process lock released.
2025-09-09 23:15:23,411 - __main__ - INFO - Bot shutdown complete.
2025-09-09 23:15:23,963 - __main__ - INFO - Main loop exiting after shutdown.
2025-09-09 23:15:23,965 - __main__ - INFO - Application exiting.
2025-09-09 23:15:28,163 - __main__ - INFO - Bot process lock acquired successfully.
2025-09-09 23:15:28,164 - __main__ - INFO - Initializing database...
2025-09-09 23:15:28,448 - event_monitor - INFO - Database initialized with clean schema. Rebuild: False
2025-09-09 23:15:28,485 - __main__ - INFO - Building application...
2025-09-09 23:15:29,324 - event_monitor - INFO - Setting up event monitor handlers...
2025-09-09 23:15:29,325 - event_monitor - INFO - Event monitor handlers setup complete.
2025-09-09 23:15:29,326 - __main__ - INFO - Starting bot polling...
2025-09-09 23:15:29,555 - __main__ - WARNING - Signal handlers SIGINT/SIGTERM not fully supported on Windows asyncio loop.
2025-09-09 23:15:29,555 - __main__ - INFO - Use Ctrl+C to trigger KeyboardInterrupt for shutdown.
2025-09-09 23:15:29,556 - __main__ - INFO - Basic SIGINT handler set using signal.signal (may have limitations).
2025-09-09 23:15:29,556 - __main__ - INFO - Signal handler setup complete.
2025-09-09 23:15:29,556 - telegram.ext.Application - INFO - Application started
2025-09-09 23:15:29,625 - __main__ - INFO - Monitor Bot started successfully!
2025-09-09 23:15:29,712 - event_monitor - INFO - Bot commands updated successfully.
2025-09-09 23:15:38,782 - event_monitor - INFO - Monitor 5eaf8f594016 saved/updated in database.
2025-09-09 23:15:38,809 - event_monitor - INFO - Started monitoring task for rsl-25-26-al-ahli-vs-al-hilal-89843-2332 (ID: 5eaf8f594016)
2025-09-09 23:15:38,810 - event_monitor - INFO - Starting monitor task for rsl-25-26-al-ahli-vs-al-hilal-89843-2332 (ID: 5eaf8f594016)
2025-09-09 23:15:39,260 - event_monitor - INFO - Channel keys updated for monitor 5eaf8f594016
2025-09-09 23:15:39,287 - event_monitor - INFO - Initial WeBook key structure stored for rsl-25-26-al-ahli-vs-al-hilal-89843-2332
2025-09-09 23:15:39,588 - event_monitor - INFO - Initial Seats.io response hash stored for RSL-25-26-Al-ahli-VS-Al-Hilal
2025-09-09 23:15:47,842 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:15:47,843 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:16:07,872 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:16:07,872 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:16:27,901 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:16:27,902 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:16:47,933 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:16:47,933 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:17:07,963 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:17:07,963 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:17:27,991 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:17:27,991 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:17:48,022 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:17:48,022 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:18:08,050 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:18:08,050 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:18:28,079 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:18:28,079 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:18:48,109 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:18:48,110 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:19:08,141 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:19:08,142 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:19:28,171 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:19:28,171 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:19:48,201 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:19:48,202 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:20:08,237 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:20:08,238 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:20:28,266 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:20:28,267 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:20:48,301 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:20:48,302 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:21:08,330 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:21:08,330 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:21:28,358 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:21:28,358 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:21:48,390 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:21:48,390 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:22:08,420 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:22:08,420 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:22:28,450 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:22:28,450 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:22:48,479 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:22:48,480 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:23:08,512 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:23:08,513 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:23:28,543 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:23:28,544 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:23:48,575 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:23:48,575 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:24:08,605 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:24:08,606 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:24:28,636 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:24:28,637 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:24:48,667 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:24:48,668 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:25:08,696 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:25:08,697 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:25:28,725 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:25:28,725 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:25:48,755 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:25:48,755 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:26:08,783 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:26:08,783 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:26:28,814 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:26:28,814 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:26:48,848 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:26:48,856 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:27:08,888 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:27:08,888 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:27:28,917 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:27:28,917 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:27:48,949 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:27:48,950 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:28:08,978 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:28:08,979 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:28:29,009 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:28:29,010 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:28:49,040 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:28:49,040 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:29:09,069 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:29:09,069 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:29:29,098 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:29:29,099 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:29:49,128 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:29:49,128 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:30:09,158 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:30:09,158 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:30:29,187 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:30:29,187 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:30:49,216 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:30:49,216 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:31:09,245 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:31:09,245 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:31:29,279 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:31:29,279 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:31:49,309 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:31:49,310 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:32:09,339 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:32:09,339 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:32:29,369 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:32:29,370 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:32:49,400 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:32:49,400 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:33:09,427 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:33:09,428 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:33:40,452 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:33:40,816 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:34:00,967 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:34:00,968 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:34:20,994 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:34:20,994 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:34:41,026 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:34:41,027 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:35:01,057 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:35:01,057 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:35:21,095 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:35:21,097 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:35:41,127 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:35:41,128 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:36:01,160 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:36:01,161 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:36:21,190 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:36:21,190 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:36:41,220 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:36:41,220 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:37:01,252 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:37:01,253 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:37:21,282 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:37:21,282 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:37:41,312 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:37:41,313 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:38:01,342 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:38:01,343 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:38:21,372 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:38:21,372 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:38:41,402 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:38:41,402 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:39:01,429 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:39:01,430 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:39:21,458 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:39:21,458 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:39:41,487 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:39:41,488 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:40:01,517 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:40:01,517 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:40:21,564 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:40:21,565 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:40:41,595 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:40:41,595 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:41:01,624 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:41:01,624 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:41:21,653 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:41:21,654 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:41:41,683 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:41:41,684 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:42:01,711 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:42:01,712 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:42:21,740 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:42:21,741 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:42:41,770 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:42:41,770 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:43:01,797 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:43:01,798 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:43:21,826 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:43:21,827 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:43:41,858 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:43:41,858 - webook_pro - INFO - Chart token refreshed: afdcbc95...
2025-09-09 23:44:01,885 - webook_pro - INFO - Successfully fetched chart token: afdcbc95...
2025-09-09 23:44:01,886 - webook_pro - INFO - Chart token refreshed: afdcbc95...
