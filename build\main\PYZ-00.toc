('E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\build\\main\\PYZ-00.pyz',
 [('OpenSSL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('PIL',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Program Files\\Python312\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Program Files\\Python312\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program Files\\Python312\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Program Files\\Python312\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Program Files\\Python312\\Lib\\_markupbase.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Program Files\\Python312\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime',
   'C:\\Program Files\\Python312\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Program Files\\Python312\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pytest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\__init__.py',
   'PYMODULE'),
  ('_pytest._argcomplete',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\_argcomplete.py',
   'PYMODULE'),
  ('_pytest._code',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\_code\\__init__.py',
   'PYMODULE'),
  ('_pytest._code.code',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\_code\\code.py',
   'PYMODULE'),
  ('_pytest._code.source',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\_code\\source.py',
   'PYMODULE'),
  ('_pytest._io',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\_io\\__init__.py',
   'PYMODULE'),
  ('_pytest._io.pprint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\_io\\pprint.py',
   'PYMODULE'),
  ('_pytest._io.saferepr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\_io\\saferepr.py',
   'PYMODULE'),
  ('_pytest._io.terminalwriter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\_io\\terminalwriter.py',
   'PYMODULE'),
  ('_pytest._io.wcwidth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\_io\\wcwidth.py',
   'PYMODULE'),
  ('_pytest._py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\_py\\__init__.py',
   'PYMODULE'),
  ('_pytest._py.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\_py\\error.py',
   'PYMODULE'),
  ('_pytest._py.path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\_py\\path.py',
   'PYMODULE'),
  ('_pytest._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\_version.py',
   'PYMODULE'),
  ('_pytest.assertion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\assertion\\__init__.py',
   'PYMODULE'),
  ('_pytest.assertion.rewrite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\assertion\\rewrite.py',
   'PYMODULE'),
  ('_pytest.assertion.truncate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\assertion\\truncate.py',
   'PYMODULE'),
  ('_pytest.assertion.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\assertion\\util.py',
   'PYMODULE'),
  ('_pytest.cacheprovider',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\cacheprovider.py',
   'PYMODULE'),
  ('_pytest.capture',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\capture.py',
   'PYMODULE'),
  ('_pytest.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\compat.py',
   'PYMODULE'),
  ('_pytest.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\config\\__init__.py',
   'PYMODULE'),
  ('_pytest.config.argparsing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\config\\argparsing.py',
   'PYMODULE'),
  ('_pytest.config.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\config\\compat.py',
   'PYMODULE'),
  ('_pytest.config.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\config\\exceptions.py',
   'PYMODULE'),
  ('_pytest.config.findpaths',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\config\\findpaths.py',
   'PYMODULE'),
  ('_pytest.debugging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\debugging.py',
   'PYMODULE'),
  ('_pytest.deprecated',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\deprecated.py',
   'PYMODULE'),
  ('_pytest.doctest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\doctest.py',
   'PYMODULE'),
  ('_pytest.faulthandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\faulthandler.py',
   'PYMODULE'),
  ('_pytest.fixtures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\fixtures.py',
   'PYMODULE'),
  ('_pytest.freeze_support',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\freeze_support.py',
   'PYMODULE'),
  ('_pytest.helpconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\helpconfig.py',
   'PYMODULE'),
  ('_pytest.hookspec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\hookspec.py',
   'PYMODULE'),
  ('_pytest.junitxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\junitxml.py',
   'PYMODULE'),
  ('_pytest.legacypath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\legacypath.py',
   'PYMODULE'),
  ('_pytest.logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\logging.py',
   'PYMODULE'),
  ('_pytest.main',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\main.py',
   'PYMODULE'),
  ('_pytest.mark',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\mark\\__init__.py',
   'PYMODULE'),
  ('_pytest.mark.expression',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\mark\\expression.py',
   'PYMODULE'),
  ('_pytest.mark.structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\mark\\structures.py',
   'PYMODULE'),
  ('_pytest.monkeypatch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\monkeypatch.py',
   'PYMODULE'),
  ('_pytest.nodes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\nodes.py',
   'PYMODULE'),
  ('_pytest.outcomes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\outcomes.py',
   'PYMODULE'),
  ('_pytest.pastebin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\pastebin.py',
   'PYMODULE'),
  ('_pytest.pathlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\pathlib.py',
   'PYMODULE'),
  ('_pytest.pytester',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\pytester.py',
   'PYMODULE'),
  ('_pytest.pytester_assertions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\pytester_assertions.py',
   'PYMODULE'),
  ('_pytest.python',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\python.py',
   'PYMODULE'),
  ('_pytest.python_api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\python_api.py',
   'PYMODULE'),
  ('_pytest.raises',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\raises.py',
   'PYMODULE'),
  ('_pytest.recwarn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\recwarn.py',
   'PYMODULE'),
  ('_pytest.reports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\reports.py',
   'PYMODULE'),
  ('_pytest.runner',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\runner.py',
   'PYMODULE'),
  ('_pytest.scope',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\scope.py',
   'PYMODULE'),
  ('_pytest.setuponly',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\setuponly.py',
   'PYMODULE'),
  ('_pytest.setupplan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\setupplan.py',
   'PYMODULE'),
  ('_pytest.skipping',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\skipping.py',
   'PYMODULE'),
  ('_pytest.stash',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\stash.py',
   'PYMODULE'),
  ('_pytest.stepwise',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\stepwise.py',
   'PYMODULE'),
  ('_pytest.terminal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\terminal.py',
   'PYMODULE'),
  ('_pytest.threadexception',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\threadexception.py',
   'PYMODULE'),
  ('_pytest.timing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\timing.py',
   'PYMODULE'),
  ('_pytest.tmpdir',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\tmpdir.py',
   'PYMODULE'),
  ('_pytest.tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\tracemalloc.py',
   'PYMODULE'),
  ('_pytest.unittest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\unittest.py',
   'PYMODULE'),
  ('_pytest.unraisableexception',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\unraisableexception.py',
   'PYMODULE'),
  ('_pytest.warning_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\warning_types.py',
   'PYMODULE'),
  ('_pytest.warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pytest\\warnings.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Program Files\\Python312\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime', 'C:\\Program Files\\Python312\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Program Files\\Python312\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('account_token_manager',
   'E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\account_token_manager.py',
   'PYMODULE'),
  ('anyio',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\anyio\\__init__.py',
   'PYMODULE'),
  ('anyio._backends',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\_backends\\__init__.py',
   'PYMODULE'),
  ('anyio._backends._asyncio',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\_backends\\_asyncio.py',
   'PYMODULE'),
  ('anyio._backends._trio',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\_backends\\_trio.py',
   'PYMODULE'),
  ('anyio._core',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\_core\\__init__.py',
   'PYMODULE'),
  ('anyio._core._compat',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\anyio\\_core\\_compat.py',
   'PYMODULE'),
  ('anyio._core._eventloop',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\_core\\_eventloop.py',
   'PYMODULE'),
  ('anyio._core._exceptions',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('anyio._core._fileio',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\anyio\\_core\\_fileio.py',
   'PYMODULE'),
  ('anyio._core._resources',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\_core\\_resources.py',
   'PYMODULE'),
  ('anyio._core._signals',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\_core\\_signals.py',
   'PYMODULE'),
  ('anyio._core._sockets',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\_core\\_sockets.py',
   'PYMODULE'),
  ('anyio._core._streams',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\_core\\_streams.py',
   'PYMODULE'),
  ('anyio._core._subprocesses',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\_core\\_subprocesses.py',
   'PYMODULE'),
  ('anyio._core._synchronization',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\_core\\_synchronization.py',
   'PYMODULE'),
  ('anyio._core._tasks',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\anyio\\_core\\_tasks.py',
   'PYMODULE'),
  ('anyio._core._testing',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\_core\\_testing.py',
   'PYMODULE'),
  ('anyio._core._typedattr',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\_core\\_typedattr.py',
   'PYMODULE'),
  ('anyio.abc',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\anyio\\abc\\__init__.py',
   'PYMODULE'),
  ('anyio.abc._resources',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\abc\\_resources.py',
   'PYMODULE'),
  ('anyio.abc._sockets',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\anyio\\abc\\_sockets.py',
   'PYMODULE'),
  ('anyio.abc._streams',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\anyio\\abc\\_streams.py',
   'PYMODULE'),
  ('anyio.abc._subprocesses',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\abc\\_subprocesses.py',
   'PYMODULE'),
  ('anyio.abc._tasks',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\anyio\\abc\\_tasks.py',
   'PYMODULE'),
  ('anyio.abc._testing',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\anyio\\abc\\_testing.py',
   'PYMODULE'),
  ('anyio.from_thread',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\anyio\\from_thread.py',
   'PYMODULE'),
  ('anyio.lowlevel',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\anyio\\lowlevel.py',
   'PYMODULE'),
  ('anyio.streams',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\streams\\__init__.py',
   'PYMODULE'),
  ('anyio.streams.memory',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\streams\\memory.py',
   'PYMODULE'),
  ('anyio.streams.stapled',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\anyio\\streams\\stapled.py',
   'PYMODULE'),
  ('anyio.streams.tls',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\anyio\\streams\\tls.py',
   'PYMODULE'),
  ('anyio.to_thread',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\anyio\\to_thread.py',
   'PYMODULE'),
  ('argparse', 'C:\\Program Files\\Python312\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Program Files\\Python312\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attrs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attrs\\__init__.py',
   'PYMODULE'),
  ('attrs.converters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attrs\\converters.py',
   'PYMODULE'),
  ('attrs.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attrs\\exceptions.py',
   'PYMODULE'),
  ('attrs.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attrs\\filters.py',
   'PYMODULE'),
  ('attrs.setters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attrs\\setters.py',
   'PYMODULE'),
  ('attrs.validators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attrs\\validators.py',
   'PYMODULE'),
  ('autocommand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\autocommand\\__init__.py',
   'PYMODULE'),
  ('autocommand.autoasync',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\autocommand\\autoasync.py',
   'PYMODULE'),
  ('autocommand.autocommand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\autocommand\\autocommand.py',
   'PYMODULE'),
  ('autocommand.automain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\autocommand\\automain.py',
   'PYMODULE'),
  ('autocommand.autoparse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\autocommand\\autoparse.py',
   'PYMODULE'),
  ('autocommand.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\autocommand\\errors.py',
   'PYMODULE'),
  ('backports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('backports.tarfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('backports.tarfile.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports.tarfile.compat.py38',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('base64', 'C:\\Program Files\\Python312\\Lib\\base64.py', 'PYMODULE'),
  ('bcrypt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('bdb', 'C:\\Program Files\\Python312\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'C:\\Program Files\\Python312\\Lib\\bisect.py', 'PYMODULE'),
  ('brotli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\brotli.py',
   'PYMODULE'),
  ('bs4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4._deprecation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\_deprecation.py',
   'PYMODULE'),
  ('bs4._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\_typing.py',
   'PYMODULE'),
  ('bs4._warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\_warnings.py',
   'PYMODULE'),
  ('bs4.builder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('bs4.dammit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\exceptions.py',
   'PYMODULE'),
  ('bs4.filter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\filter.py',
   'PYMODULE'),
  ('bs4.formatter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2', 'C:\\Program Files\\Python312\\Lib\\bz2.py', 'PYMODULE'),
  ('cachetools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cachetools\\__init__.py',
   'PYMODULE'),
  ('cachetools.keys',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cachetools\\keys.py',
   'PYMODULE'),
  ('calendar', 'C:\\Program Files\\Python312\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi', 'C:\\Program Files\\Python312\\Lib\\cgi.py', 'PYMODULE'),
  ('chardet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.codingstatemachinedict',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\codingstatemachinedict.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.macromanprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\macromanprober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.resultdict',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\resultdict.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('chart_token_manager',
   'E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\chart_token_manager.py',
   'PYMODULE'),
  ('click',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cmd', 'C:\\Program Files\\Python312\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\Program Files\\Python312\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Program Files\\Python312\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'C:\\Program Files\\Python312\\Lib\\colorsys.py', 'PYMODULE'),
  ('concurrent',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('config',
   'E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\config.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Program Files\\Python312\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Program Files\\Python312\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Program Files\\Python312\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'C:\\Program Files\\Python312\\Lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cssselect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cssselect\\__init__.py',
   'PYMODULE'),
  ('cssselect.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cssselect\\parser.py',
   'PYMODULE'),
  ('cssselect.xpath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cssselect\\xpath.py',
   'PYMODULE'),
  ('csv', 'C:\\Program Files\\Python312\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'C:\\Program Files\\Python312\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Program Files\\Python312\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program Files\\Python312\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Program Files\\Python312\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Program Files\\Python312\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Program Files\\Python312\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Program Files\\Python312\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Program Files\\Python312\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Program Files\\Python312\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses',
   'C:\\Program Files\\Python312\\Lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'C:\\Program Files\\Python312\\Lib\\curses\\has_key.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Program Files\\Python312\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'C:\\Program Files\\Python312\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\Program Files\\Python312\\Lib\\decimal.py', 'PYMODULE'),
  ('defusedxml',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('difflib', 'C:\\Program Files\\Python312\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\Program Files\\Python312\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'C:\\Program Files\\Python312\\Lib\\doctest.py', 'PYMODULE'),
  ('dotenv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('elitesoftworks',
   'E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\elitesoftworks.py',
   'PYMODULE'),
  ('email',
   'C:\\Program Files\\Python312\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program Files\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program Files\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program Files\\Python312\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program Files\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program Files\\Python312\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program Files\\Python312\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program Files\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program Files\\Python312\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program Files\\Python312\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program Files\\Python312\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program Files\\Python312\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program Files\\Python312\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program Files\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program Files\\Python312\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program Files\\Python312\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program Files\\Python312\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program Files\\Python312\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program Files\\Python312\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program Files\\Python312\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('event_monitor',
   'E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\event_monitor.py',
   'PYMODULE'),
  ('exceptiongroup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\exceptiongroup\\__init__.py',
   'PYMODULE'),
  ('exceptiongroup._catch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\exceptiongroup\\_catch.py',
   'PYMODULE'),
  ('exceptiongroup._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\exceptiongroup\\_exceptions.py',
   'PYMODULE'),
  ('exceptiongroup._formatting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\exceptiongroup\\_formatting.py',
   'PYMODULE'),
  ('exceptiongroup._suppress',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\exceptiongroup\\_suppress.py',
   'PYMODULE'),
  ('exceptiongroup._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\exceptiongroup\\_version.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Program Files\\Python312\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Program Files\\Python312\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Program Files\\Python312\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Program Files\\Python312\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Program Files\\Python312\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Program Files\\Python312\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Program Files\\Python312\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\Program Files\\Python312\\Lib\\gzip.py', 'PYMODULE'),
  ('h11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._abnf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11._connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11._headers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._readers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._state',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('h11._writers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('h2',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\h2\\__init__.py',
   'PYMODULE'),
  ('h2.config',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\h2\\config.py',
   'PYMODULE'),
  ('h2.connection',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\h2\\connection.py',
   'PYMODULE'),
  ('h2.errors',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\h2\\errors.py',
   'PYMODULE'),
  ('h2.events',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\h2\\events.py',
   'PYMODULE'),
  ('h2.exceptions',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\h2\\exceptions.py',
   'PYMODULE'),
  ('h2.frame_buffer',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\h2\\frame_buffer.py',
   'PYMODULE'),
  ('h2.settings',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\h2\\settings.py',
   'PYMODULE'),
  ('h2.stream',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\h2\\stream.py',
   'PYMODULE'),
  ('h2.utilities',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\h2\\utilities.py',
   'PYMODULE'),
  ('h2.windows',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\h2\\windows.py',
   'PYMODULE'),
  ('hashlib', 'C:\\Program Files\\Python312\\Lib\\hashlib.py', 'PYMODULE'),
  ('helper',
   'E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\helper.py',
   'PYMODULE'),
  ('hmac', 'C:\\Program Files\\Python312\\Lib\\hmac.py', 'PYMODULE'),
  ('hpack',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\hpack\\__init__.py',
   'PYMODULE'),
  ('hpack.compat',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\hpack\\compat.py',
   'PYMODULE'),
  ('hpack.exceptions',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\hpack\\exceptions.py',
   'PYMODULE'),
  ('hpack.hpack',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\hpack\\hpack.py',
   'PYMODULE'),
  ('hpack.huffman',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\hpack\\huffman.py',
   'PYMODULE'),
  ('hpack.huffman_constants',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\hpack\\huffman_constants.py',
   'PYMODULE'),
  ('hpack.huffman_table',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\hpack\\huffman_table.py',
   'PYMODULE'),
  ('hpack.struct',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\hpack\\struct.py',
   'PYMODULE'),
  ('hpack.table',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\hpack\\table.py',
   'PYMODULE'),
  ('html', 'C:\\Program Files\\Python312\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\Program Files\\Python312\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Program Files\\Python312\\Lib\\html\\parser.py',
   'PYMODULE'),
  ('http', 'C:\\Program Files\\Python312\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'C:\\Program Files\\Python312\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Program Files\\Python312\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Program Files\\Python312\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Program Files\\Python312\\Lib\\http\\server.py',
   'PYMODULE'),
  ('httpcore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\__init__.py',
   'PYMODULE'),
  ('httpcore._api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_api.py',
   'PYMODULE'),
  ('httpcore._async',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_async\\__init__.py',
   'PYMODULE'),
  ('httpcore._async.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_async\\connection.py',
   'PYMODULE'),
  ('httpcore._async.connection_pool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_async\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._async.http11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_async\\http11.py',
   'PYMODULE'),
  ('httpcore._async.http2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_async\\http2.py',
   'PYMODULE'),
  ('httpcore._async.http_proxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_async\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._async.interfaces',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_async\\interfaces.py',
   'PYMODULE'),
  ('httpcore._async.socks_proxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_async\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._backends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_backends\\__init__.py',
   'PYMODULE'),
  ('httpcore._backends.anyio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_backends\\anyio.py',
   'PYMODULE'),
  ('httpcore._backends.auto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_backends\\auto.py',
   'PYMODULE'),
  ('httpcore._backends.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_backends\\base.py',
   'PYMODULE'),
  ('httpcore._backends.mock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_backends\\mock.py',
   'PYMODULE'),
  ('httpcore._backends.sync',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_backends\\sync.py',
   'PYMODULE'),
  ('httpcore._backends.trio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_backends\\trio.py',
   'PYMODULE'),
  ('httpcore._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_exceptions.py',
   'PYMODULE'),
  ('httpcore._models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_models.py',
   'PYMODULE'),
  ('httpcore._ssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_ssl.py',
   'PYMODULE'),
  ('httpcore._sync',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_sync\\__init__.py',
   'PYMODULE'),
  ('httpcore._sync.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_sync\\connection.py',
   'PYMODULE'),
  ('httpcore._sync.connection_pool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_sync\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._sync.http11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_sync\\http11.py',
   'PYMODULE'),
  ('httpcore._sync.http2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_sync\\http2.py',
   'PYMODULE'),
  ('httpcore._sync.http_proxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_sync\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._sync.interfaces',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_sync\\interfaces.py',
   'PYMODULE'),
  ('httpcore._sync.socks_proxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_sync\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._synchronization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_synchronization.py',
   'PYMODULE'),
  ('httpcore._trace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_trace.py',
   'PYMODULE'),
  ('httpcore._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpcore\\_utils.py',
   'PYMODULE'),
  ('httpx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\__init__.py',
   'PYMODULE'),
  ('httpx.__version__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\__version__.py',
   'PYMODULE'),
  ('httpx._api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_api.py',
   'PYMODULE'),
  ('httpx._auth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_auth.py',
   'PYMODULE'),
  ('httpx._client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_client.py',
   'PYMODULE'),
  ('httpx._config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_config.py',
   'PYMODULE'),
  ('httpx._content',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_content.py',
   'PYMODULE'),
  ('httpx._decoders',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_decoders.py',
   'PYMODULE'),
  ('httpx._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_exceptions.py',
   'PYMODULE'),
  ('httpx._main',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_main.py',
   'PYMODULE'),
  ('httpx._models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_models.py',
   'PYMODULE'),
  ('httpx._multipart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_multipart.py',
   'PYMODULE'),
  ('httpx._status_codes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_status_codes.py',
   'PYMODULE'),
  ('httpx._transports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_transports\\__init__.py',
   'PYMODULE'),
  ('httpx._transports.asgi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_transports\\asgi.py',
   'PYMODULE'),
  ('httpx._transports.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_transports\\base.py',
   'PYMODULE'),
  ('httpx._transports.default',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_transports\\default.py',
   'PYMODULE'),
  ('httpx._transports.mock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_transports\\mock.py',
   'PYMODULE'),
  ('httpx._transports.wsgi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_transports\\wsgi.py',
   'PYMODULE'),
  ('httpx._types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_types.py',
   'PYMODULE'),
  ('httpx._urlparse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_urlparse.py',
   'PYMODULE'),
  ('httpx._urls',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_urls.py',
   'PYMODULE'),
  ('httpx._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\httpx\\_utils.py',
   'PYMODULE'),
  ('hwid', 'C:\\Program Files\\Python312\\Lib\\hwid.py', 'PYMODULE'),
  ('hyperframe',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\hyperframe\\__init__.py',
   'PYMODULE'),
  ('hyperframe.exceptions',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\hyperframe\\exceptions.py',
   'PYMODULE'),
  ('hyperframe.flags',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\hyperframe\\flags.py',
   'PYMODULE'),
  ('hyperframe.frame',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\hyperframe\\frame.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Program Files\\Python312\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Program Files\\Python312\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program Files\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program Files\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program Files\\Python312\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program Files\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Program Files\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program Files\\Python312\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib_metadata',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata._typing',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\importlib_metadata\\_typing.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('iniconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\iniconfig\\__init__.py',
   'PYMODULE'),
  ('iniconfig._parse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\iniconfig\\_parse.py',
   'PYMODULE'),
  ('iniconfig.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\iniconfig\\exceptions.py',
   'PYMODULE'),
  ('inspect', 'C:\\Program Files\\Python312\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Program Files\\Python312\\Lib\\ipaddress.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jaraco.context',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('jaraco.functools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('jaraco.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('json', 'C:\\Program Files\\Python312\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'C:\\Program Files\\Python312\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Program Files\\Python312\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Program Files\\Python312\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Program Files\\Python312\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Program Files\\Python312\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma', 'C:\\Program Files\\Python312\\Lib\\lzma.py', 'PYMODULE'),
  ('markdown_it',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\markdown_it\\__init__.py',
   'PYMODULE'),
  ('markdown_it._compat',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\markdown_it\\_compat.py',
   'PYMODULE'),
  ('markdown_it._punycode',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\_punycode.py',
   'PYMODULE'),
  ('markdown_it.common',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\common\\__init__.py',
   'PYMODULE'),
  ('markdown_it.common.entities',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\common\\entities.py',
   'PYMODULE'),
  ('markdown_it.common.html_blocks',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\common\\html_blocks.py',
   'PYMODULE'),
  ('markdown_it.common.html_re',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\common\\html_re.py',
   'PYMODULE'),
  ('markdown_it.common.normalize_url',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\common\\normalize_url.py',
   'PYMODULE'),
  ('markdown_it.common.utils',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\common\\utils.py',
   'PYMODULE'),
  ('markdown_it.helpers',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\helpers\\__init__.py',
   'PYMODULE'),
  ('markdown_it.helpers.parse_link_destination',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\helpers\\parse_link_destination.py',
   'PYMODULE'),
  ('markdown_it.helpers.parse_link_label',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\helpers\\parse_link_label.py',
   'PYMODULE'),
  ('markdown_it.helpers.parse_link_title',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\helpers\\parse_link_title.py',
   'PYMODULE'),
  ('markdown_it.main',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\markdown_it\\main.py',
   'PYMODULE'),
  ('markdown_it.parser_block',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\parser_block.py',
   'PYMODULE'),
  ('markdown_it.parser_core',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\parser_core.py',
   'PYMODULE'),
  ('markdown_it.parser_inline',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\parser_inline.py',
   'PYMODULE'),
  ('markdown_it.presets',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\presets\\__init__.py',
   'PYMODULE'),
  ('markdown_it.presets.commonmark',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\presets\\commonmark.py',
   'PYMODULE'),
  ('markdown_it.presets.default',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\presets\\default.py',
   'PYMODULE'),
  ('markdown_it.presets.zero',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\presets\\zero.py',
   'PYMODULE'),
  ('markdown_it.renderer',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\markdown_it\\renderer.py',
   'PYMODULE'),
  ('markdown_it.ruler',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\markdown_it\\ruler.py',
   'PYMODULE'),
  ('markdown_it.rules_block',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_block\\__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_block.blockquote',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_block\\blockquote.py',
   'PYMODULE'),
  ('markdown_it.rules_block.code',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_block\\code.py',
   'PYMODULE'),
  ('markdown_it.rules_block.fence',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_block\\fence.py',
   'PYMODULE'),
  ('markdown_it.rules_block.heading',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_block\\heading.py',
   'PYMODULE'),
  ('markdown_it.rules_block.hr',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_block\\hr.py',
   'PYMODULE'),
  ('markdown_it.rules_block.html_block',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_block\\html_block.py',
   'PYMODULE'),
  ('markdown_it.rules_block.lheading',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_block\\lheading.py',
   'PYMODULE'),
  ('markdown_it.rules_block.list',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_block\\list.py',
   'PYMODULE'),
  ('markdown_it.rules_block.paragraph',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_block\\paragraph.py',
   'PYMODULE'),
  ('markdown_it.rules_block.reference',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_block\\reference.py',
   'PYMODULE'),
  ('markdown_it.rules_block.state_block',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_block\\state_block.py',
   'PYMODULE'),
  ('markdown_it.rules_block.table',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_block\\table.py',
   'PYMODULE'),
  ('markdown_it.rules_core',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_core\\__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_core.block',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_core\\block.py',
   'PYMODULE'),
  ('markdown_it.rules_core.inline',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_core\\inline.py',
   'PYMODULE'),
  ('markdown_it.rules_core.linkify',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_core\\linkify.py',
   'PYMODULE'),
  ('markdown_it.rules_core.normalize',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_core\\normalize.py',
   'PYMODULE'),
  ('markdown_it.rules_core.replacements',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_core\\replacements.py',
   'PYMODULE'),
  ('markdown_it.rules_core.smartquotes',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_core\\smartquotes.py',
   'PYMODULE'),
  ('markdown_it.rules_core.state_core',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_core\\state_core.py',
   'PYMODULE'),
  ('markdown_it.rules_core.text_join',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_core\\text_join.py',
   'PYMODULE'),
  ('markdown_it.rules_inline',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_inline\\__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.autolink',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_inline\\autolink.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.backticks',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_inline\\backticks.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.balance_pairs',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_inline\\balance_pairs.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.emphasis',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_inline\\emphasis.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.entity',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_inline\\entity.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.escape',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_inline\\escape.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.fragments_join',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_inline\\fragments_join.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.html_inline',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_inline\\html_inline.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.image',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_inline\\image.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.link',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_inline\\link.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.linkify',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_inline\\linkify.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.newline',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_inline\\newline.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.state_inline',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_inline\\state_inline.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.strikethrough',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_inline\\strikethrough.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.text',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\markdown_it\\rules_inline\\text.py',
   'PYMODULE'),
  ('markdown_it.token',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\markdown_it\\token.py',
   'PYMODULE'),
  ('markdown_it.utils',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\markdown_it\\utils.py',
   'PYMODULE'),
  ('mdurl',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\mdurl\\__init__.py',
   'PYMODULE'),
  ('mdurl._decode',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\mdurl\\_decode.py',
   'PYMODULE'),
  ('mdurl._encode',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\mdurl\\_encode.py',
   'PYMODULE'),
  ('mdurl._format',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\mdurl\\_format.py',
   'PYMODULE'),
  ('mdurl._parse',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\mdurl\\_parse.py',
   'PYMODULE'),
  ('mdurl._url',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\mdurl\\_url.py',
   'PYMODULE'),
  ('mimetypes', 'C:\\Program Files\\Python312\\Lib\\mimetypes.py', 'PYMODULE'),
  ('more_itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('more_itertools.more',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('more_itertools.recipes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\Program Files\\Python312\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'C:\\Program Files\\Python312\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers', 'C:\\Program Files\\Python312\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.array_api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'C:\\Program Files\\Python312\\Lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'C:\\Program Files\\Python312\\Lib\\optparse.py', 'PYMODULE'),
  ('outcome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\outcome\\__init__.py',
   'PYMODULE'),
  ('outcome._impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\outcome\\_impl.py',
   'PYMODULE'),
  ('outcome._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\outcome\\_util.py',
   'PYMODULE'),
  ('outcome._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\outcome\\_version.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Program Files\\Python312\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'C:\\Program Files\\Python312\\Lib\\pdb.py', 'PYMODULE'),
  ('pexpect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pexpect\\__init__.py',
   'PYMODULE'),
  ('pexpect._async',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pexpect\\_async.py',
   'PYMODULE'),
  ('pexpect._async_pre_await',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pexpect\\_async_pre_await.py',
   'PYMODULE'),
  ('pexpect._async_w_await',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pexpect\\_async_w_await.py',
   'PYMODULE'),
  ('pexpect.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pexpect\\exceptions.py',
   'PYMODULE'),
  ('pexpect.expect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pexpect\\expect.py',
   'PYMODULE'),
  ('pexpect.pty_spawn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pexpect\\pty_spawn.py',
   'PYMODULE'),
  ('pexpect.run',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pexpect\\run.py',
   'PYMODULE'),
  ('pexpect.spawnbase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pexpect\\spawnbase.py',
   'PYMODULE'),
  ('pexpect.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pexpect\\utils.py',
   'PYMODULE'),
  ('pickle', 'C:\\Program Files\\Python312\\Lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'C:\\Program Files\\Python312\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Program Files\\Python312\\Lib\\platform.py', 'PYMODULE'),
  ('platformdirs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\platformdirs\\unix.py',
   'PYMODULE'),
  ('platformdirs.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('plistlib', 'C:\\Program Files\\Python312\\Lib\\plistlib.py', 'PYMODULE'),
  ('pluggy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pluggy\\__init__.py',
   'PYMODULE'),
  ('pluggy._callers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pluggy\\_callers.py',
   'PYMODULE'),
  ('pluggy._hooks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pluggy\\_hooks.py',
   'PYMODULE'),
  ('pluggy._manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pluggy\\_manager.py',
   'PYMODULE'),
  ('pluggy._result',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pluggy\\_result.py',
   'PYMODULE'),
  ('pluggy._tracing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pluggy\\_tracing.py',
   'PYMODULE'),
  ('pluggy._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pluggy\\_version.py',
   'PYMODULE'),
  ('pluggy._warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pluggy\\_warnings.py',
   'PYMODULE'),
  ('pprint', 'C:\\Program Files\\Python312\\Lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('pty', 'C:\\Program Files\\Python312\\Lib\\pty.py', 'PYMODULE'),
  ('ptyprocess',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\ptyprocess\\__init__.py',
   'PYMODULE'),
  ('ptyprocess._fork_pty',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\ptyprocess\\_fork_pty.py',
   'PYMODULE'),
  ('ptyprocess.ptyprocess',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\ptyprocess\\ptyprocess.py',
   'PYMODULE'),
  ('ptyprocess.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\ptyprocess\\util.py',
   'PYMODULE'),
  ('py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\py.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Program Files\\Python312\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'C:\\Program Files\\Python312\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'C:\\Program Files\\Python312\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Program Files\\Python312\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyfiglet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyfiglet\\__init__.py',
   'PYMODULE'),
  ('pyfiglet.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyfiglet\\version.py',
   'PYMODULE'),
  ('pygments',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\__init__.py',
   'PYMODULE'),
  ('pygments.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\console.py',
   'PYMODULE'),
  ('pygments.filter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\filter.py',
   'PYMODULE'),
  ('pygments.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\filters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\formatter.py',
   'PYMODULE'),
  ('pygments.formatters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\formatters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatters._mapping',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\formatters\\_mapping.py',
   'PYMODULE'),
  ('pygments.formatters.bbcode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\formatters\\bbcode.py',
   'PYMODULE'),
  ('pygments.formatters.groff',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\formatters\\groff.py',
   'PYMODULE'),
  ('pygments.formatters.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\formatters\\html.py',
   'PYMODULE'),
  ('pygments.formatters.img',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\formatters\\img.py',
   'PYMODULE'),
  ('pygments.formatters.irc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\formatters\\irc.py',
   'PYMODULE'),
  ('pygments.formatters.latex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\formatters\\latex.py',
   'PYMODULE'),
  ('pygments.formatters.other',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\formatters\\other.py',
   'PYMODULE'),
  ('pygments.formatters.pangomarkup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\formatters\\pangomarkup.py',
   'PYMODULE'),
  ('pygments.formatters.rtf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\formatters\\rtf.py',
   'PYMODULE'),
  ('pygments.formatters.svg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\formatters\\svg.py',
   'PYMODULE'),
  ('pygments.formatters.terminal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\formatters\\terminal.py',
   'PYMODULE'),
  ('pygments.formatters.terminal256',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\formatters\\terminal256.py',
   'PYMODULE'),
  ('pygments.lexer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexer.py',
   'PYMODULE'),
  ('pygments.lexers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\__init__.py',
   'PYMODULE'),
  ('pygments.lexers._ada_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_ada_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._asy_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_asy_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cl_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_cl_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cocoa_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_cocoa_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._csound_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_csound_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._css_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_css_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._googlesql_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_googlesql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._julia_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_julia_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lasso_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_lasso_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lilypond_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_lilypond_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lua_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_lua_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._luau_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_luau_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mapping',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_mapping.py',
   'PYMODULE'),
  ('pygments.lexers._mql_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_mql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mysql_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_mysql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._openedge_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_openedge_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._php_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_php_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._postgres_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_postgres_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._qlik_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_qlik_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scheme_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_scheme_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scilab_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_scilab_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sourcemod_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_sourcemod_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sql_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_sql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stan_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_stan_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stata_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_stata_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._tsql_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_tsql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._usd_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_usd_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vbscript_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_vbscript_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vim_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\_vim_builtins.py',
   'PYMODULE'),
  ('pygments.lexers.actionscript',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\actionscript.py',
   'PYMODULE'),
  ('pygments.lexers.ada',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\ada.py',
   'PYMODULE'),
  ('pygments.lexers.agile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\agile.py',
   'PYMODULE'),
  ('pygments.lexers.algebra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\algebra.py',
   'PYMODULE'),
  ('pygments.lexers.ambient',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\ambient.py',
   'PYMODULE'),
  ('pygments.lexers.amdgpu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\amdgpu.py',
   'PYMODULE'),
  ('pygments.lexers.ampl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\ampl.py',
   'PYMODULE'),
  ('pygments.lexers.apdlexer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\apdlexer.py',
   'PYMODULE'),
  ('pygments.lexers.apl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\apl.py',
   'PYMODULE'),
  ('pygments.lexers.archetype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\archetype.py',
   'PYMODULE'),
  ('pygments.lexers.arrow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\arrow.py',
   'PYMODULE'),
  ('pygments.lexers.arturo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\arturo.py',
   'PYMODULE'),
  ('pygments.lexers.asc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\asc.py',
   'PYMODULE'),
  ('pygments.lexers.asm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\asm.py',
   'PYMODULE'),
  ('pygments.lexers.asn1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\asn1.py',
   'PYMODULE'),
  ('pygments.lexers.automation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\automation.py',
   'PYMODULE'),
  ('pygments.lexers.bare',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\bare.py',
   'PYMODULE'),
  ('pygments.lexers.basic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\basic.py',
   'PYMODULE'),
  ('pygments.lexers.bdd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\bdd.py',
   'PYMODULE'),
  ('pygments.lexers.berry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\berry.py',
   'PYMODULE'),
  ('pygments.lexers.bibtex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\bibtex.py',
   'PYMODULE'),
  ('pygments.lexers.blueprint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\blueprint.py',
   'PYMODULE'),
  ('pygments.lexers.boa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\boa.py',
   'PYMODULE'),
  ('pygments.lexers.bqn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\bqn.py',
   'PYMODULE'),
  ('pygments.lexers.business',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\business.py',
   'PYMODULE'),
  ('pygments.lexers.c_cpp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\c_cpp.py',
   'PYMODULE'),
  ('pygments.lexers.c_like',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\c_like.py',
   'PYMODULE'),
  ('pygments.lexers.capnproto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\capnproto.py',
   'PYMODULE'),
  ('pygments.lexers.carbon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\carbon.py',
   'PYMODULE'),
  ('pygments.lexers.cddl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\cddl.py',
   'PYMODULE'),
  ('pygments.lexers.chapel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\chapel.py',
   'PYMODULE'),
  ('pygments.lexers.clean',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\clean.py',
   'PYMODULE'),
  ('pygments.lexers.codeql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\codeql.py',
   'PYMODULE'),
  ('pygments.lexers.comal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\comal.py',
   'PYMODULE'),
  ('pygments.lexers.compiled',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\compiled.py',
   'PYMODULE'),
  ('pygments.lexers.configs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\configs.py',
   'PYMODULE'),
  ('pygments.lexers.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\console.py',
   'PYMODULE'),
  ('pygments.lexers.cplint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\cplint.py',
   'PYMODULE'),
  ('pygments.lexers.crystal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\crystal.py',
   'PYMODULE'),
  ('pygments.lexers.csound',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\csound.py',
   'PYMODULE'),
  ('pygments.lexers.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\css.py',
   'PYMODULE'),
  ('pygments.lexers.d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\d.py',
   'PYMODULE'),
  ('pygments.lexers.dalvik',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\dalvik.py',
   'PYMODULE'),
  ('pygments.lexers.data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\data.py',
   'PYMODULE'),
  ('pygments.lexers.dax',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\dax.py',
   'PYMODULE'),
  ('pygments.lexers.devicetree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\devicetree.py',
   'PYMODULE'),
  ('pygments.lexers.diff',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\diff.py',
   'PYMODULE'),
  ('pygments.lexers.dns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\dns.py',
   'PYMODULE'),
  ('pygments.lexers.dotnet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\dotnet.py',
   'PYMODULE'),
  ('pygments.lexers.dsls',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\dsls.py',
   'PYMODULE'),
  ('pygments.lexers.dylan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\dylan.py',
   'PYMODULE'),
  ('pygments.lexers.ecl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\ecl.py',
   'PYMODULE'),
  ('pygments.lexers.eiffel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\eiffel.py',
   'PYMODULE'),
  ('pygments.lexers.elm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\elm.py',
   'PYMODULE'),
  ('pygments.lexers.elpi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\elpi.py',
   'PYMODULE'),
  ('pygments.lexers.email',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\email.py',
   'PYMODULE'),
  ('pygments.lexers.erlang',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\erlang.py',
   'PYMODULE'),
  ('pygments.lexers.esoteric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\esoteric.py',
   'PYMODULE'),
  ('pygments.lexers.ezhil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\ezhil.py',
   'PYMODULE'),
  ('pygments.lexers.factor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\factor.py',
   'PYMODULE'),
  ('pygments.lexers.fantom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\fantom.py',
   'PYMODULE'),
  ('pygments.lexers.felix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\felix.py',
   'PYMODULE'),
  ('pygments.lexers.fift',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\fift.py',
   'PYMODULE'),
  ('pygments.lexers.floscript',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\floscript.py',
   'PYMODULE'),
  ('pygments.lexers.forth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\forth.py',
   'PYMODULE'),
  ('pygments.lexers.fortran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\fortran.py',
   'PYMODULE'),
  ('pygments.lexers.foxpro',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\foxpro.py',
   'PYMODULE'),
  ('pygments.lexers.freefem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\freefem.py',
   'PYMODULE'),
  ('pygments.lexers.func',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\func.py',
   'PYMODULE'),
  ('pygments.lexers.functional',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\functional.py',
   'PYMODULE'),
  ('pygments.lexers.futhark',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\futhark.py',
   'PYMODULE'),
  ('pygments.lexers.gcodelexer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\gcodelexer.py',
   'PYMODULE'),
  ('pygments.lexers.gdscript',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\gdscript.py',
   'PYMODULE'),
  ('pygments.lexers.gleam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\gleam.py',
   'PYMODULE'),
  ('pygments.lexers.go',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\go.py',
   'PYMODULE'),
  ('pygments.lexers.grammar_notation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\grammar_notation.py',
   'PYMODULE'),
  ('pygments.lexers.graph',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\graph.py',
   'PYMODULE'),
  ('pygments.lexers.graphics',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\graphics.py',
   'PYMODULE'),
  ('pygments.lexers.graphql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\graphql.py',
   'PYMODULE'),
  ('pygments.lexers.graphviz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\graphviz.py',
   'PYMODULE'),
  ('pygments.lexers.gsql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\gsql.py',
   'PYMODULE'),
  ('pygments.lexers.hare',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\hare.py',
   'PYMODULE'),
  ('pygments.lexers.haskell',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\haskell.py',
   'PYMODULE'),
  ('pygments.lexers.haxe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\haxe.py',
   'PYMODULE'),
  ('pygments.lexers.hdl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\hdl.py',
   'PYMODULE'),
  ('pygments.lexers.hexdump',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\hexdump.py',
   'PYMODULE'),
  ('pygments.lexers.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\html.py',
   'PYMODULE'),
  ('pygments.lexers.idl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\idl.py',
   'PYMODULE'),
  ('pygments.lexers.igor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\igor.py',
   'PYMODULE'),
  ('pygments.lexers.inferno',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\inferno.py',
   'PYMODULE'),
  ('pygments.lexers.installers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\installers.py',
   'PYMODULE'),
  ('pygments.lexers.int_fiction',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\int_fiction.py',
   'PYMODULE'),
  ('pygments.lexers.iolang',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\iolang.py',
   'PYMODULE'),
  ('pygments.lexers.j',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\j.py',
   'PYMODULE'),
  ('pygments.lexers.javascript',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\javascript.py',
   'PYMODULE'),
  ('pygments.lexers.jmespath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\jmespath.py',
   'PYMODULE'),
  ('pygments.lexers.jslt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\jslt.py',
   'PYMODULE'),
  ('pygments.lexers.json5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\json5.py',
   'PYMODULE'),
  ('pygments.lexers.jsonnet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\jsonnet.py',
   'PYMODULE'),
  ('pygments.lexers.jsx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\jsx.py',
   'PYMODULE'),
  ('pygments.lexers.julia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\julia.py',
   'PYMODULE'),
  ('pygments.lexers.jvm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\jvm.py',
   'PYMODULE'),
  ('pygments.lexers.kuin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\kuin.py',
   'PYMODULE'),
  ('pygments.lexers.kusto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\kusto.py',
   'PYMODULE'),
  ('pygments.lexers.ldap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\ldap.py',
   'PYMODULE'),
  ('pygments.lexers.lean',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\lean.py',
   'PYMODULE'),
  ('pygments.lexers.lilypond',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\lilypond.py',
   'PYMODULE'),
  ('pygments.lexers.lisp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\lisp.py',
   'PYMODULE'),
  ('pygments.lexers.macaulay2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\macaulay2.py',
   'PYMODULE'),
  ('pygments.lexers.make',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\make.py',
   'PYMODULE'),
  ('pygments.lexers.maple',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\maple.py',
   'PYMODULE'),
  ('pygments.lexers.markup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\markup.py',
   'PYMODULE'),
  ('pygments.lexers.math',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\math.py',
   'PYMODULE'),
  ('pygments.lexers.matlab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\matlab.py',
   'PYMODULE'),
  ('pygments.lexers.maxima',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\maxima.py',
   'PYMODULE'),
  ('pygments.lexers.meson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\meson.py',
   'PYMODULE'),
  ('pygments.lexers.mime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\mime.py',
   'PYMODULE'),
  ('pygments.lexers.minecraft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\minecraft.py',
   'PYMODULE'),
  ('pygments.lexers.mips',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\mips.py',
   'PYMODULE'),
  ('pygments.lexers.ml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\ml.py',
   'PYMODULE'),
  ('pygments.lexers.modeling',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\modeling.py',
   'PYMODULE'),
  ('pygments.lexers.modula2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\modula2.py',
   'PYMODULE'),
  ('pygments.lexers.mojo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\mojo.py',
   'PYMODULE'),
  ('pygments.lexers.monte',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\monte.py',
   'PYMODULE'),
  ('pygments.lexers.mosel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\mosel.py',
   'PYMODULE'),
  ('pygments.lexers.ncl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\ncl.py',
   'PYMODULE'),
  ('pygments.lexers.nimrod',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\nimrod.py',
   'PYMODULE'),
  ('pygments.lexers.nit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\nit.py',
   'PYMODULE'),
  ('pygments.lexers.nix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\nix.py',
   'PYMODULE'),
  ('pygments.lexers.numbair',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\numbair.py',
   'PYMODULE'),
  ('pygments.lexers.oberon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\oberon.py',
   'PYMODULE'),
  ('pygments.lexers.objective',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\objective.py',
   'PYMODULE'),
  ('pygments.lexers.ooc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\ooc.py',
   'PYMODULE'),
  ('pygments.lexers.openscad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\openscad.py',
   'PYMODULE'),
  ('pygments.lexers.other',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\other.py',
   'PYMODULE'),
  ('pygments.lexers.parasail',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\parasail.py',
   'PYMODULE'),
  ('pygments.lexers.parsers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\parsers.py',
   'PYMODULE'),
  ('pygments.lexers.pascal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\pascal.py',
   'PYMODULE'),
  ('pygments.lexers.pawn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\pawn.py',
   'PYMODULE'),
  ('pygments.lexers.pddl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\pddl.py',
   'PYMODULE'),
  ('pygments.lexers.perl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\perl.py',
   'PYMODULE'),
  ('pygments.lexers.phix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\phix.py',
   'PYMODULE'),
  ('pygments.lexers.php',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\php.py',
   'PYMODULE'),
  ('pygments.lexers.pointless',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\pointless.py',
   'PYMODULE'),
  ('pygments.lexers.pony',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\pony.py',
   'PYMODULE'),
  ('pygments.lexers.praat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\praat.py',
   'PYMODULE'),
  ('pygments.lexers.procfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\procfile.py',
   'PYMODULE'),
  ('pygments.lexers.prolog',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\prolog.py',
   'PYMODULE'),
  ('pygments.lexers.promql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\promql.py',
   'PYMODULE'),
  ('pygments.lexers.prql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\prql.py',
   'PYMODULE'),
  ('pygments.lexers.ptx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\ptx.py',
   'PYMODULE'),
  ('pygments.lexers.python',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\python.py',
   'PYMODULE'),
  ('pygments.lexers.q',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\q.py',
   'PYMODULE'),
  ('pygments.lexers.qlik',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\qlik.py',
   'PYMODULE'),
  ('pygments.lexers.qvt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\qvt.py',
   'PYMODULE'),
  ('pygments.lexers.r',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\r.py',
   'PYMODULE'),
  ('pygments.lexers.rdf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\rdf.py',
   'PYMODULE'),
  ('pygments.lexers.rebol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\rebol.py',
   'PYMODULE'),
  ('pygments.lexers.rego',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\rego.py',
   'PYMODULE'),
  ('pygments.lexers.resource',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\resource.py',
   'PYMODULE'),
  ('pygments.lexers.ride',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\ride.py',
   'PYMODULE'),
  ('pygments.lexers.rita',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\rita.py',
   'PYMODULE'),
  ('pygments.lexers.rnc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\rnc.py',
   'PYMODULE'),
  ('pygments.lexers.roboconf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\roboconf.py',
   'PYMODULE'),
  ('pygments.lexers.robotframework',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\robotframework.py',
   'PYMODULE'),
  ('pygments.lexers.ruby',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\ruby.py',
   'PYMODULE'),
  ('pygments.lexers.rust',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\rust.py',
   'PYMODULE'),
  ('pygments.lexers.sas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\sas.py',
   'PYMODULE'),
  ('pygments.lexers.savi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\savi.py',
   'PYMODULE'),
  ('pygments.lexers.scdoc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\scdoc.py',
   'PYMODULE'),
  ('pygments.lexers.scripting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\scripting.py',
   'PYMODULE'),
  ('pygments.lexers.sgf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\sgf.py',
   'PYMODULE'),
  ('pygments.lexers.shell',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\shell.py',
   'PYMODULE'),
  ('pygments.lexers.sieve',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\sieve.py',
   'PYMODULE'),
  ('pygments.lexers.slash',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\slash.py',
   'PYMODULE'),
  ('pygments.lexers.smalltalk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\smalltalk.py',
   'PYMODULE'),
  ('pygments.lexers.smithy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\smithy.py',
   'PYMODULE'),
  ('pygments.lexers.smv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\smv.py',
   'PYMODULE'),
  ('pygments.lexers.snobol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\snobol.py',
   'PYMODULE'),
  ('pygments.lexers.solidity',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\solidity.py',
   'PYMODULE'),
  ('pygments.lexers.soong',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\soong.py',
   'PYMODULE'),
  ('pygments.lexers.sophia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\sophia.py',
   'PYMODULE'),
  ('pygments.lexers.special',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\special.py',
   'PYMODULE'),
  ('pygments.lexers.spice',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\spice.py',
   'PYMODULE'),
  ('pygments.lexers.sql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\sql.py',
   'PYMODULE'),
  ('pygments.lexers.srcinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\srcinfo.py',
   'PYMODULE'),
  ('pygments.lexers.stata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\stata.py',
   'PYMODULE'),
  ('pygments.lexers.supercollider',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\supercollider.py',
   'PYMODULE'),
  ('pygments.lexers.tablegen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\tablegen.py',
   'PYMODULE'),
  ('pygments.lexers.tact',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\tact.py',
   'PYMODULE'),
  ('pygments.lexers.tal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\tal.py',
   'PYMODULE'),
  ('pygments.lexers.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\tcl.py',
   'PYMODULE'),
  ('pygments.lexers.teal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\teal.py',
   'PYMODULE'),
  ('pygments.lexers.templates',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\templates.py',
   'PYMODULE'),
  ('pygments.lexers.teraterm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\teraterm.py',
   'PYMODULE'),
  ('pygments.lexers.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\testing.py',
   'PYMODULE'),
  ('pygments.lexers.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\text.py',
   'PYMODULE'),
  ('pygments.lexers.textedit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\textedit.py',
   'PYMODULE'),
  ('pygments.lexers.textfmts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\textfmts.py',
   'PYMODULE'),
  ('pygments.lexers.theorem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\theorem.py',
   'PYMODULE'),
  ('pygments.lexers.thingsdb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\thingsdb.py',
   'PYMODULE'),
  ('pygments.lexers.tlb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\tlb.py',
   'PYMODULE'),
  ('pygments.lexers.tls',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\tls.py',
   'PYMODULE'),
  ('pygments.lexers.tnt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\tnt.py',
   'PYMODULE'),
  ('pygments.lexers.trafficscript',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\trafficscript.py',
   'PYMODULE'),
  ('pygments.lexers.typoscript',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\typoscript.py',
   'PYMODULE'),
  ('pygments.lexers.typst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\typst.py',
   'PYMODULE'),
  ('pygments.lexers.ul4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\ul4.py',
   'PYMODULE'),
  ('pygments.lexers.unicon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\unicon.py',
   'PYMODULE'),
  ('pygments.lexers.urbi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\urbi.py',
   'PYMODULE'),
  ('pygments.lexers.usd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\usd.py',
   'PYMODULE'),
  ('pygments.lexers.varnish',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\varnish.py',
   'PYMODULE'),
  ('pygments.lexers.verification',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\verification.py',
   'PYMODULE'),
  ('pygments.lexers.verifpal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\verifpal.py',
   'PYMODULE'),
  ('pygments.lexers.vip',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\vip.py',
   'PYMODULE'),
  ('pygments.lexers.vyper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\vyper.py',
   'PYMODULE'),
  ('pygments.lexers.web',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\web.py',
   'PYMODULE'),
  ('pygments.lexers.webassembly',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\webassembly.py',
   'PYMODULE'),
  ('pygments.lexers.webidl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\webidl.py',
   'PYMODULE'),
  ('pygments.lexers.webmisc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\webmisc.py',
   'PYMODULE'),
  ('pygments.lexers.wgsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\wgsl.py',
   'PYMODULE'),
  ('pygments.lexers.whiley',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\whiley.py',
   'PYMODULE'),
  ('pygments.lexers.wowtoc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\wowtoc.py',
   'PYMODULE'),
  ('pygments.lexers.wren',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\wren.py',
   'PYMODULE'),
  ('pygments.lexers.x10',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\x10.py',
   'PYMODULE'),
  ('pygments.lexers.xorg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\xorg.py',
   'PYMODULE'),
  ('pygments.lexers.yang',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\yang.py',
   'PYMODULE'),
  ('pygments.lexers.yara',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\yara.py',
   'PYMODULE'),
  ('pygments.lexers.zig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\lexers\\zig.py',
   'PYMODULE'),
  ('pygments.modeline',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\modeline.py',
   'PYMODULE'),
  ('pygments.plugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\plugin.py',
   'PYMODULE'),
  ('pygments.regexopt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\regexopt.py',
   'PYMODULE'),
  ('pygments.scanner',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\scanner.py',
   'PYMODULE'),
  ('pygments.style',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\style.py',
   'PYMODULE'),
  ('pygments.styles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\__init__.py',
   'PYMODULE'),
  ('pygments.styles._mapping',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\_mapping.py',
   'PYMODULE'),
  ('pygments.styles.abap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\abap.py',
   'PYMODULE'),
  ('pygments.styles.algol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\algol.py',
   'PYMODULE'),
  ('pygments.styles.algol_nu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\algol_nu.py',
   'PYMODULE'),
  ('pygments.styles.arduino',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\arduino.py',
   'PYMODULE'),
  ('pygments.styles.autumn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\autumn.py',
   'PYMODULE'),
  ('pygments.styles.borland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\borland.py',
   'PYMODULE'),
  ('pygments.styles.bw',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\bw.py',
   'PYMODULE'),
  ('pygments.styles.coffee',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\coffee.py',
   'PYMODULE'),
  ('pygments.styles.colorful',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\colorful.py',
   'PYMODULE'),
  ('pygments.styles.default',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\default.py',
   'PYMODULE'),
  ('pygments.styles.dracula',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\dracula.py',
   'PYMODULE'),
  ('pygments.styles.emacs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\emacs.py',
   'PYMODULE'),
  ('pygments.styles.friendly',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\friendly.py',
   'PYMODULE'),
  ('pygments.styles.friendly_grayscale',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\friendly_grayscale.py',
   'PYMODULE'),
  ('pygments.styles.fruity',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\fruity.py',
   'PYMODULE'),
  ('pygments.styles.gh_dark',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\gh_dark.py',
   'PYMODULE'),
  ('pygments.styles.gruvbox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\gruvbox.py',
   'PYMODULE'),
  ('pygments.styles.igor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\igor.py',
   'PYMODULE'),
  ('pygments.styles.inkpot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\inkpot.py',
   'PYMODULE'),
  ('pygments.styles.lightbulb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\lightbulb.py',
   'PYMODULE'),
  ('pygments.styles.lilypond',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\lilypond.py',
   'PYMODULE'),
  ('pygments.styles.lovelace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\lovelace.py',
   'PYMODULE'),
  ('pygments.styles.manni',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\manni.py',
   'PYMODULE'),
  ('pygments.styles.material',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\material.py',
   'PYMODULE'),
  ('pygments.styles.monokai',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\monokai.py',
   'PYMODULE'),
  ('pygments.styles.murphy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\murphy.py',
   'PYMODULE'),
  ('pygments.styles.native',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\native.py',
   'PYMODULE'),
  ('pygments.styles.nord',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\nord.py',
   'PYMODULE'),
  ('pygments.styles.onedark',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\onedark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_dark',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\paraiso_dark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_light',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\paraiso_light.py',
   'PYMODULE'),
  ('pygments.styles.pastie',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\pastie.py',
   'PYMODULE'),
  ('pygments.styles.perldoc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\perldoc.py',
   'PYMODULE'),
  ('pygments.styles.rainbow_dash',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\rainbow_dash.py',
   'PYMODULE'),
  ('pygments.styles.rrt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\rrt.py',
   'PYMODULE'),
  ('pygments.styles.sas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\sas.py',
   'PYMODULE'),
  ('pygments.styles.solarized',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\solarized.py',
   'PYMODULE'),
  ('pygments.styles.staroffice',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\staroffice.py',
   'PYMODULE'),
  ('pygments.styles.stata_dark',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\stata_dark.py',
   'PYMODULE'),
  ('pygments.styles.stata_light',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\stata_light.py',
   'PYMODULE'),
  ('pygments.styles.tango',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\tango.py',
   'PYMODULE'),
  ('pygments.styles.trac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\trac.py',
   'PYMODULE'),
  ('pygments.styles.vim',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\vim.py',
   'PYMODULE'),
  ('pygments.styles.vs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\vs.py',
   'PYMODULE'),
  ('pygments.styles.xcode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\xcode.py',
   'PYMODULE'),
  ('pygments.styles.zenburn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\styles\\zenburn.py',
   'PYMODULE'),
  ('pygments.token',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\token.py',
   'PYMODULE'),
  ('pygments.unistring',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\unistring.py',
   'PYMODULE'),
  ('pygments.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pygments\\util.py',
   'PYMODULE'),
  ('pyreadline3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('pytest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytest\\__init__.py',
   'PYMODULE'),
  ('pytz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'C:\\Program Files\\Python312\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Program Files\\Python312\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Program Files\\Python312\\Lib\\random.py', 'PYMODULE'),
  ('readline',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\readline.py',
   'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\__init__.py',
   'PYMODULE'),
  ('rich.__main__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\__main__.py',
   'PYMODULE'),
  ('rich._cell_widths',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_cell_widths.py',
   'PYMODULE'),
  ('rich._emoji_codes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_emoji_codes.py',
   'PYMODULE'),
  ('rich._emoji_replace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_emoji_replace.py',
   'PYMODULE'),
  ('rich._export_format',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_export_format.py',
   'PYMODULE'),
  ('rich._extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_extension.py',
   'PYMODULE'),
  ('rich._fileno',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_fileno.py',
   'PYMODULE'),
  ('rich._inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_inspect.py',
   'PYMODULE'),
  ('rich._log_render',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_log_render.py',
   'PYMODULE'),
  ('rich._loop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_loop.py',
   'PYMODULE'),
  ('rich._null_file',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_null_file.py',
   'PYMODULE'),
  ('rich._palettes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_palettes.py',
   'PYMODULE'),
  ('rich._pick',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_pick.py',
   'PYMODULE'),
  ('rich._ratio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_ratio.py',
   'PYMODULE'),
  ('rich._spinners',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_spinners.py',
   'PYMODULE'),
  ('rich._stack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_stack.py',
   'PYMODULE'),
  ('rich._timer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_timer.py',
   'PYMODULE'),
  ('rich._win32_console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_win32_console.py',
   'PYMODULE'),
  ('rich._windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_windows.py',
   'PYMODULE'),
  ('rich._windows_renderer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_windows_renderer.py',
   'PYMODULE'),
  ('rich._wrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\_wrap.py',
   'PYMODULE'),
  ('rich.abc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\abc.py',
   'PYMODULE'),
  ('rich.align',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\align.py',
   'PYMODULE'),
  ('rich.ansi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\ansi.py',
   'PYMODULE'),
  ('rich.box',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\box.py',
   'PYMODULE'),
  ('rich.cells',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\cells.py',
   'PYMODULE'),
  ('rich.color',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\color.py',
   'PYMODULE'),
  ('rich.color_triplet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\color_triplet.py',
   'PYMODULE'),
  ('rich.columns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\columns.py',
   'PYMODULE'),
  ('rich.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\console.py',
   'PYMODULE'),
  ('rich.constrain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\constrain.py',
   'PYMODULE'),
  ('rich.containers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\containers.py',
   'PYMODULE'),
  ('rich.control',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\control.py',
   'PYMODULE'),
  ('rich.default_styles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\default_styles.py',
   'PYMODULE'),
  ('rich.emoji',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\emoji.py',
   'PYMODULE'),
  ('rich.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\errors.py',
   'PYMODULE'),
  ('rich.file_proxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\file_proxy.py',
   'PYMODULE'),
  ('rich.filesize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\filesize.py',
   'PYMODULE'),
  ('rich.highlighter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\highlighter.py',
   'PYMODULE'),
  ('rich.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\json.py',
   'PYMODULE'),
  ('rich.jupyter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\jupyter.py',
   'PYMODULE'),
  ('rich.live',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\live.py',
   'PYMODULE'),
  ('rich.live_render',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\live_render.py',
   'PYMODULE'),
  ('rich.markdown',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\markdown.py',
   'PYMODULE'),
  ('rich.markup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\markup.py',
   'PYMODULE'),
  ('rich.measure',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\measure.py',
   'PYMODULE'),
  ('rich.padding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\padding.py',
   'PYMODULE'),
  ('rich.pager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\pager.py',
   'PYMODULE'),
  ('rich.palette',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\palette.py',
   'PYMODULE'),
  ('rich.panel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\panel.py',
   'PYMODULE'),
  ('rich.pretty',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\pretty.py',
   'PYMODULE'),
  ('rich.progress',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\progress.py',
   'PYMODULE'),
  ('rich.progress_bar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\progress_bar.py',
   'PYMODULE'),
  ('rich.protocol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\protocol.py',
   'PYMODULE'),
  ('rich.region',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\region.py',
   'PYMODULE'),
  ('rich.repr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\repr.py',
   'PYMODULE'),
  ('rich.rule',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\rule.py',
   'PYMODULE'),
  ('rich.scope',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\scope.py',
   'PYMODULE'),
  ('rich.screen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\screen.py',
   'PYMODULE'),
  ('rich.segment',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\segment.py',
   'PYMODULE'),
  ('rich.spinner',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\spinner.py',
   'PYMODULE'),
  ('rich.status',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\status.py',
   'PYMODULE'),
  ('rich.style',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\style.py',
   'PYMODULE'),
  ('rich.styled',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\styled.py',
   'PYMODULE'),
  ('rich.syntax',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\syntax.py',
   'PYMODULE'),
  ('rich.table',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\table.py',
   'PYMODULE'),
  ('rich.terminal_theme',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\terminal_theme.py',
   'PYMODULE'),
  ('rich.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\text.py',
   'PYMODULE'),
  ('rich.theme',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\theme.py',
   'PYMODULE'),
  ('rich.themes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\themes.py',
   'PYMODULE'),
  ('rich.traceback',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\rich\\traceback.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Program Files\\Python312\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'C:\\Program Files\\Python312\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\Program Files\\Python312\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Program Files\\Python312\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.cygwin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\compilers\\C\\cygwin.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.unix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\compilers\\C\\unix.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.zos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\compilers\\C\\zos.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.tests',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.support',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\support.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_archive_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_bdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_bdist_dumb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_bdist_rpm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_build.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_build_clib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_build_ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_build_py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_build_scripts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_check',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_check.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_clean',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_clean.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_cmd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_config_cmd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_config_cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_core.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_dir_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_dist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_dist.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_extension.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_file_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_filelist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_install',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_install.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_install_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_install_headers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_install_lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_install_scripts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_log.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_modified',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_modified.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_sdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_spawn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_sysconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_text_file',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_util.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_version.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.test_versionpredicate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\test_versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.tests.unix_compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\tests\\unix_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.zosccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\zosccompiler.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.autocommand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\autocommand\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.autocommand.autoasync',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\autocommand\\autoasync.py',
   'PYMODULE'),
  ('setuptools._vendor.autocommand.autocommand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\autocommand\\autocommand.py',
   'PYMODULE'),
  ('setuptools._vendor.autocommand.automain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\autocommand\\automain.py',
   'PYMODULE'),
  ('setuptools._vendor.autocommand.autoparse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\autocommand\\autoparse.py',
   'PYMODULE'),
  ('setuptools._vendor.autocommand.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\autocommand\\errors.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.__main__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__main__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.diagnose',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\diagnose.py',
   'PYMODULE'),
  ('setuptools._vendor.inflect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\inflect\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.inflect.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\inflect\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.inflect.compat.py38',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\inflect\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.licenses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.licenses._spdx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.__main__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\platformdirs\\__main__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._checkers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_checkers.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_config.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_decorators.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_exceptions.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_functions.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._importhook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_importhook.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._memo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_memo.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._pytest_plugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_pytest_plugin.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._suppression',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_suppression.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._transformer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_transformer.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._union_transformer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_union_transformer.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_utils.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.__main__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\__main__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel._bdist_wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\_bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel._setuptools_logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\_setuptools_logging.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.bdist_wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'C:\\Program Files\\Python312\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Program Files\\Python312\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Program Files\\Python312\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'C:\\Program Files\\Python312\\Lib\\site.py', 'PYMODULE'),
  ('smtplib', 'C:\\Program Files\\Python312\\Lib\\smtplib.py', 'PYMODULE'),
  ('sniffio',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('socket', 'C:\\Program Files\\Python312\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'C:\\Program Files\\Python312\\Lib\\socketserver.py',
   'PYMODULE'),
  ('socks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socks.py',
   'PYMODULE'),
  ('socksio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socksio\\__init__.py',
   'PYMODULE'),
  ('socksio._types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socksio\\_types.py',
   'PYMODULE'),
  ('socksio.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socksio\\compat.py',
   'PYMODULE'),
  ('socksio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socksio\\exceptions.py',
   'PYMODULE'),
  ('socksio.socks4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socksio\\socks4.py',
   'PYMODULE'),
  ('socksio.socks5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socksio\\socks5.py',
   'PYMODULE'),
  ('socksio.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socksio\\utils.py',
   'PYMODULE'),
  ('sortedcontainers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sortedcontainers\\__init__.py',
   'PYMODULE'),
  ('sortedcontainers.sorteddict',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sortedcontainers\\sorteddict.py',
   'PYMODULE'),
  ('sortedcontainers.sortedlist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sortedcontainers\\sortedlist.py',
   'PYMODULE'),
  ('sortedcontainers.sortedset',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sortedcontainers\\sortedset.py',
   'PYMODULE'),
  ('soupsieve',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Program Files\\Python312\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'C:\\Program Files\\Python312\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Program Files\\Python312\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Program Files\\Python312\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'C:\\Program Files\\Python312\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'C:\\Program Files\\Python312\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'C:\\Program Files\\Python312\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'C:\\Program Files\\Python312\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Program Files\\Python312\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig', 'C:\\Program Files\\Python312\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'C:\\Program Files\\Python312\\Lib\\tarfile.py', 'PYMODULE'),
  ('telegram',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\__init__.py',
   'PYMODULE'),
  ('telegram._bot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_bot.py',
   'PYMODULE'),
  ('telegram._botcommand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_botcommand.py',
   'PYMODULE'),
  ('telegram._botcommandscope',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_botcommandscope.py',
   'PYMODULE'),
  ('telegram._botdescription',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_botdescription.py',
   'PYMODULE'),
  ('telegram._botname',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_botname.py',
   'PYMODULE'),
  ('telegram._callbackquery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_callbackquery.py',
   'PYMODULE'),
  ('telegram._chat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_chat.py',
   'PYMODULE'),
  ('telegram._chatadministratorrights',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_chatadministratorrights.py',
   'PYMODULE'),
  ('telegram._chatboost',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_chatboost.py',
   'PYMODULE'),
  ('telegram._chatinvitelink',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_chatinvitelink.py',
   'PYMODULE'),
  ('telegram._chatjoinrequest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_chatjoinrequest.py',
   'PYMODULE'),
  ('telegram._chatlocation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_chatlocation.py',
   'PYMODULE'),
  ('telegram._chatmember',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_chatmember.py',
   'PYMODULE'),
  ('telegram._chatmemberupdated',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_chatmemberupdated.py',
   'PYMODULE'),
  ('telegram._chatpermissions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_chatpermissions.py',
   'PYMODULE'),
  ('telegram._choseninlineresult',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_choseninlineresult.py',
   'PYMODULE'),
  ('telegram._dice',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_dice.py',
   'PYMODULE'),
  ('telegram._files',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\__init__.py',
   'PYMODULE'),
  ('telegram._files._basemedium',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\_basemedium.py',
   'PYMODULE'),
  ('telegram._files._basethumbedmedium',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\_basethumbedmedium.py',
   'PYMODULE'),
  ('telegram._files.animation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\animation.py',
   'PYMODULE'),
  ('telegram._files.audio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\audio.py',
   'PYMODULE'),
  ('telegram._files.chatphoto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\chatphoto.py',
   'PYMODULE'),
  ('telegram._files.contact',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\contact.py',
   'PYMODULE'),
  ('telegram._files.document',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\document.py',
   'PYMODULE'),
  ('telegram._files.file',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\file.py',
   'PYMODULE'),
  ('telegram._files.inputfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\inputfile.py',
   'PYMODULE'),
  ('telegram._files.inputmedia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\inputmedia.py',
   'PYMODULE'),
  ('telegram._files.inputsticker',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\inputsticker.py',
   'PYMODULE'),
  ('telegram._files.location',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\location.py',
   'PYMODULE'),
  ('telegram._files.photosize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\photosize.py',
   'PYMODULE'),
  ('telegram._files.sticker',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\sticker.py',
   'PYMODULE'),
  ('telegram._files.venue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\venue.py',
   'PYMODULE'),
  ('telegram._files.video',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\video.py',
   'PYMODULE'),
  ('telegram._files.videonote',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\videonote.py',
   'PYMODULE'),
  ('telegram._files.voice',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_files\\voice.py',
   'PYMODULE'),
  ('telegram._forcereply',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_forcereply.py',
   'PYMODULE'),
  ('telegram._forumtopic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_forumtopic.py',
   'PYMODULE'),
  ('telegram._games',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_games\\__init__.py',
   'PYMODULE'),
  ('telegram._games.callbackgame',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_games\\callbackgame.py',
   'PYMODULE'),
  ('telegram._games.game',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_games\\game.py',
   'PYMODULE'),
  ('telegram._games.gamehighscore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_games\\gamehighscore.py',
   'PYMODULE'),
  ('telegram._giveaway',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_giveaway.py',
   'PYMODULE'),
  ('telegram._inline',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\__init__.py',
   'PYMODULE'),
  ('telegram._inline.inlinekeyboardbutton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinekeyboardbutton.py',
   'PYMODULE'),
  ('telegram._inline.inlinekeyboardmarkup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinekeyboardmarkup.py',
   'PYMODULE'),
  ('telegram._inline.inlinequery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequery.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresult',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresult.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultarticle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultarticle.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultaudio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultaudio.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultcachedaudio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultcachedaudio.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultcacheddocument',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultcacheddocument.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultcachedgif',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultcachedgif.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultcachedmpeg4gif',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultcachedmpeg4gif.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultcachedphoto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultcachedphoto.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultcachedsticker',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultcachedsticker.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultcachedvideo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultcachedvideo.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultcachedvoice',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultcachedvoice.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultcontact',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultcontact.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultdocument',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultdocument.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultgame',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultgame.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultgif',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultgif.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultlocation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultlocation.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultmpeg4gif',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultmpeg4gif.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultphoto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultphoto.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultsbutton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultsbutton.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultvenue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultvenue.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultvideo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultvideo.py',
   'PYMODULE'),
  ('telegram._inline.inlinequeryresultvoice',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inlinequeryresultvoice.py',
   'PYMODULE'),
  ('telegram._inline.inputcontactmessagecontent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inputcontactmessagecontent.py',
   'PYMODULE'),
  ('telegram._inline.inputinvoicemessagecontent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inputinvoicemessagecontent.py',
   'PYMODULE'),
  ('telegram._inline.inputlocationmessagecontent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inputlocationmessagecontent.py',
   'PYMODULE'),
  ('telegram._inline.inputmessagecontent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inputmessagecontent.py',
   'PYMODULE'),
  ('telegram._inline.inputtextmessagecontent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inputtextmessagecontent.py',
   'PYMODULE'),
  ('telegram._inline.inputvenuemessagecontent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_inline\\inputvenuemessagecontent.py',
   'PYMODULE'),
  ('telegram._keyboardbutton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_keyboardbutton.py',
   'PYMODULE'),
  ('telegram._keyboardbuttonpolltype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_keyboardbuttonpolltype.py',
   'PYMODULE'),
  ('telegram._keyboardbuttonrequest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_keyboardbuttonrequest.py',
   'PYMODULE'),
  ('telegram._linkpreviewoptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_linkpreviewoptions.py',
   'PYMODULE'),
  ('telegram._loginurl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_loginurl.py',
   'PYMODULE'),
  ('telegram._menubutton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_menubutton.py',
   'PYMODULE'),
  ('telegram._message',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_message.py',
   'PYMODULE'),
  ('telegram._messageautodeletetimerchanged',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_messageautodeletetimerchanged.py',
   'PYMODULE'),
  ('telegram._messageentity',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_messageentity.py',
   'PYMODULE'),
  ('telegram._messageid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_messageid.py',
   'PYMODULE'),
  ('telegram._messageorigin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_messageorigin.py',
   'PYMODULE'),
  ('telegram._messagereactionupdated',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_messagereactionupdated.py',
   'PYMODULE'),
  ('telegram._passport',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_passport\\__init__.py',
   'PYMODULE'),
  ('telegram._passport.credentials',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_passport\\credentials.py',
   'PYMODULE'),
  ('telegram._passport.data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_passport\\data.py',
   'PYMODULE'),
  ('telegram._passport.encryptedpassportelement',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_passport\\encryptedpassportelement.py',
   'PYMODULE'),
  ('telegram._passport.passportdata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_passport\\passportdata.py',
   'PYMODULE'),
  ('telegram._passport.passportelementerrors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_passport\\passportelementerrors.py',
   'PYMODULE'),
  ('telegram._passport.passportfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_passport\\passportfile.py',
   'PYMODULE'),
  ('telegram._payment',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_payment\\__init__.py',
   'PYMODULE'),
  ('telegram._payment.invoice',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_payment\\invoice.py',
   'PYMODULE'),
  ('telegram._payment.labeledprice',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_payment\\labeledprice.py',
   'PYMODULE'),
  ('telegram._payment.orderinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_payment\\orderinfo.py',
   'PYMODULE'),
  ('telegram._payment.precheckoutquery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_payment\\precheckoutquery.py',
   'PYMODULE'),
  ('telegram._payment.shippingaddress',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_payment\\shippingaddress.py',
   'PYMODULE'),
  ('telegram._payment.shippingoption',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_payment\\shippingoption.py',
   'PYMODULE'),
  ('telegram._payment.shippingquery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_payment\\shippingquery.py',
   'PYMODULE'),
  ('telegram._payment.successfulpayment',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_payment\\successfulpayment.py',
   'PYMODULE'),
  ('telegram._poll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_poll.py',
   'PYMODULE'),
  ('telegram._proximityalerttriggered',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_proximityalerttriggered.py',
   'PYMODULE'),
  ('telegram._reaction',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_reaction.py',
   'PYMODULE'),
  ('telegram._reply',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_reply.py',
   'PYMODULE'),
  ('telegram._replykeyboardmarkup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_replykeyboardmarkup.py',
   'PYMODULE'),
  ('telegram._replykeyboardremove',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_replykeyboardremove.py',
   'PYMODULE'),
  ('telegram._sentwebappmessage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_sentwebappmessage.py',
   'PYMODULE'),
  ('telegram._shared',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_shared.py',
   'PYMODULE'),
  ('telegram._story',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_story.py',
   'PYMODULE'),
  ('telegram._switchinlinequerychosenchat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_switchinlinequerychosenchat.py',
   'PYMODULE'),
  ('telegram._telegramobject',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_telegramobject.py',
   'PYMODULE'),
  ('telegram._update',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_update.py',
   'PYMODULE'),
  ('telegram._user',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_user.py',
   'PYMODULE'),
  ('telegram._userprofilephotos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_userprofilephotos.py',
   'PYMODULE'),
  ('telegram._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_utils\\__init__.py',
   'PYMODULE'),
  ('telegram._utils.argumentparsing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_utils\\argumentparsing.py',
   'PYMODULE'),
  ('telegram._utils.datetime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_utils\\datetime.py',
   'PYMODULE'),
  ('telegram._utils.defaultvalue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_utils\\defaultvalue.py',
   'PYMODULE'),
  ('telegram._utils.enum',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_utils\\enum.py',
   'PYMODULE'),
  ('telegram._utils.files',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_utils\\files.py',
   'PYMODULE'),
  ('telegram._utils.logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_utils\\logging.py',
   'PYMODULE'),
  ('telegram._utils.markup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_utils\\markup.py',
   'PYMODULE'),
  ('telegram._utils.repr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_utils\\repr.py',
   'PYMODULE'),
  ('telegram._utils.strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_utils\\strings.py',
   'PYMODULE'),
  ('telegram._utils.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_utils\\types.py',
   'PYMODULE'),
  ('telegram._utils.warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_utils\\warnings.py',
   'PYMODULE'),
  ('telegram._utils.warnings_transition',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_utils\\warnings_transition.py',
   'PYMODULE'),
  ('telegram._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_version.py',
   'PYMODULE'),
  ('telegram._videochat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_videochat.py',
   'PYMODULE'),
  ('telegram._webappdata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_webappdata.py',
   'PYMODULE'),
  ('telegram._webappinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_webappinfo.py',
   'PYMODULE'),
  ('telegram._webhookinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_webhookinfo.py',
   'PYMODULE'),
  ('telegram._writeaccessallowed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\_writeaccessallowed.py',
   'PYMODULE'),
  ('telegram.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\constants.py',
   'PYMODULE'),
  ('telegram.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\error.py',
   'PYMODULE'),
  ('telegram.ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\__init__.py',
   'PYMODULE'),
  ('telegram.ext._aioratelimiter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_aioratelimiter.py',
   'PYMODULE'),
  ('telegram.ext._application',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_application.py',
   'PYMODULE'),
  ('telegram.ext._applicationbuilder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_applicationbuilder.py',
   'PYMODULE'),
  ('telegram.ext._basepersistence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_basepersistence.py',
   'PYMODULE'),
  ('telegram.ext._baseratelimiter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_baseratelimiter.py',
   'PYMODULE'),
  ('telegram.ext._baseupdateprocessor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_baseupdateprocessor.py',
   'PYMODULE'),
  ('telegram.ext._callbackcontext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_callbackcontext.py',
   'PYMODULE'),
  ('telegram.ext._callbackdatacache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_callbackdatacache.py',
   'PYMODULE'),
  ('telegram.ext._contexttypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_contexttypes.py',
   'PYMODULE'),
  ('telegram.ext._defaults',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_defaults.py',
   'PYMODULE'),
  ('telegram.ext._dictpersistence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_dictpersistence.py',
   'PYMODULE'),
  ('telegram.ext._extbot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_extbot.py',
   'PYMODULE'),
  ('telegram.ext._handlers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\__init__.py',
   'PYMODULE'),
  ('telegram.ext._handlers.basehandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\basehandler.py',
   'PYMODULE'),
  ('telegram.ext._handlers.callbackqueryhandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\callbackqueryhandler.py',
   'PYMODULE'),
  ('telegram.ext._handlers.chatboosthandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\chatboosthandler.py',
   'PYMODULE'),
  ('telegram.ext._handlers.chatjoinrequesthandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\chatjoinrequesthandler.py',
   'PYMODULE'),
  ('telegram.ext._handlers.chatmemberhandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\chatmemberhandler.py',
   'PYMODULE'),
  ('telegram.ext._handlers.choseninlineresulthandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\choseninlineresulthandler.py',
   'PYMODULE'),
  ('telegram.ext._handlers.commandhandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\commandhandler.py',
   'PYMODULE'),
  ('telegram.ext._handlers.conversationhandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\conversationhandler.py',
   'PYMODULE'),
  ('telegram.ext._handlers.inlinequeryhandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\inlinequeryhandler.py',
   'PYMODULE'),
  ('telegram.ext._handlers.messagehandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\messagehandler.py',
   'PYMODULE'),
  ('telegram.ext._handlers.messagereactionhandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\messagereactionhandler.py',
   'PYMODULE'),
  ('telegram.ext._handlers.pollanswerhandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\pollanswerhandler.py',
   'PYMODULE'),
  ('telegram.ext._handlers.pollhandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\pollhandler.py',
   'PYMODULE'),
  ('telegram.ext._handlers.precheckoutqueryhandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\precheckoutqueryhandler.py',
   'PYMODULE'),
  ('telegram.ext._handlers.prefixhandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\prefixhandler.py',
   'PYMODULE'),
  ('telegram.ext._handlers.shippingqueryhandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\shippingqueryhandler.py',
   'PYMODULE'),
  ('telegram.ext._handlers.stringcommandhandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\stringcommandhandler.py',
   'PYMODULE'),
  ('telegram.ext._handlers.stringregexhandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\stringregexhandler.py',
   'PYMODULE'),
  ('telegram.ext._handlers.typehandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_handlers\\typehandler.py',
   'PYMODULE'),
  ('telegram.ext._jobqueue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_jobqueue.py',
   'PYMODULE'),
  ('telegram.ext._picklepersistence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_picklepersistence.py',
   'PYMODULE'),
  ('telegram.ext._updater',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_updater.py',
   'PYMODULE'),
  ('telegram.ext._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_utils\\__init__.py',
   'PYMODULE'),
  ('telegram.ext._utils._update_parsing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_utils\\_update_parsing.py',
   'PYMODULE'),
  ('telegram.ext._utils.stack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_utils\\stack.py',
   'PYMODULE'),
  ('telegram.ext._utils.trackingdict',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_utils\\trackingdict.py',
   'PYMODULE'),
  ('telegram.ext._utils.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_utils\\types.py',
   'PYMODULE'),
  ('telegram.ext._utils.webhookhandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\_utils\\webhookhandler.py',
   'PYMODULE'),
  ('telegram.ext.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\ext\\filters.py',
   'PYMODULE'),
  ('telegram.helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\helpers.py',
   'PYMODULE'),
  ('telegram.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\request\\__init__.py',
   'PYMODULE'),
  ('telegram.request._baserequest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\request\\_baserequest.py',
   'PYMODULE'),
  ('telegram.request._httpxrequest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\request\\_httpxrequest.py',
   'PYMODULE'),
  ('telegram.request._requestdata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\request\\_requestdata.py',
   'PYMODULE'),
  ('telegram.request._requestparameter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\request\\_requestparameter.py',
   'PYMODULE'),
  ('telegram.warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\telegram\\warnings.py',
   'PYMODULE'),
  ('tempfile', 'C:\\Program Files\\Python312\\Lib\\tempfile.py', 'PYMODULE'),
  ('test', 'C:\\Program Files\\Python312\\Lib\\test\\__init__.py', 'PYMODULE'),
  ('test.support',
   'C:\\Program Files\\Python312\\Lib\\test\\support\\__init__.py',
   'PYMODULE'),
  ('test.support.import_helper',
   'C:\\Program Files\\Python312\\Lib\\test\\support\\import_helper.py',
   'PYMODULE'),
  ('test.support.os_helper',
   'C:\\Program Files\\Python312\\Lib\\test\\support\\os_helper.py',
   'PYMODULE'),
  ('test.support.testresult',
   'C:\\Program Files\\Python312\\Lib\\test\\support\\testresult.py',
   'PYMODULE'),
  ('test.support.warnings_helper',
   'C:\\Program Files\\Python312\\Lib\\test\\support\\warnings_helper.py',
   'PYMODULE'),
  ('textwrap', 'C:\\Program Files\\Python312\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Program Files\\Python312\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'C:\\Program Files\\Python312\\Lib\\token.py', 'PYMODULE'),
  ('token_retrieval',
   'E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\token_retrieval.py',
   'PYMODULE'),
  ('tokenize', 'C:\\Program Files\\Python312\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('tomli._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('tomli._re',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomli._types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('tomllib',
   'C:\\Program Files\\Python312\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\Program Files\\Python312\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\Program Files\\Python312\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\Program Files\\Python312\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tornado',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\__init__.py',
   'PYMODULE'),
  ('tornado._locale_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\_locale_data.py',
   'PYMODULE'),
  ('tornado.autoreload',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\autoreload.py',
   'PYMODULE'),
  ('tornado.concurrent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\concurrent.py',
   'PYMODULE'),
  ('tornado.escape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\escape.py',
   'PYMODULE'),
  ('tornado.gen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\gen.py',
   'PYMODULE'),
  ('tornado.http1connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\http1connection.py',
   'PYMODULE'),
  ('tornado.httpserver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\httpserver.py',
   'PYMODULE'),
  ('tornado.httputil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\httputil.py',
   'PYMODULE'),
  ('tornado.ioloop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\ioloop.py',
   'PYMODULE'),
  ('tornado.iostream',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\iostream.py',
   'PYMODULE'),
  ('tornado.locale',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\locale.py',
   'PYMODULE'),
  ('tornado.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\log.py',
   'PYMODULE'),
  ('tornado.netutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\netutil.py',
   'PYMODULE'),
  ('tornado.options',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\options.py',
   'PYMODULE'),
  ('tornado.platform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\platform\\__init__.py',
   'PYMODULE'),
  ('tornado.platform.asyncio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\platform\\asyncio.py',
   'PYMODULE'),
  ('tornado.process',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\process.py',
   'PYMODULE'),
  ('tornado.routing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\routing.py',
   'PYMODULE'),
  ('tornado.tcpserver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\tcpserver.py',
   'PYMODULE'),
  ('tornado.template',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\template.py',
   'PYMODULE'),
  ('tornado.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\util.py',
   'PYMODULE'),
  ('tornado.web',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\web.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Program Files\\Python312\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('trio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\__init__.py',
   'PYMODULE'),
  ('trio._abc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_abc.py',
   'PYMODULE'),
  ('trio._channel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_channel.py',
   'PYMODULE'),
  ('trio._core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\__init__.py',
   'PYMODULE'),
  ('trio._core._asyncgens',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_asyncgens.py',
   'PYMODULE'),
  ('trio._core._concat_tb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_concat_tb.py',
   'PYMODULE'),
  ('trio._core._entry_queue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_entry_queue.py',
   'PYMODULE'),
  ('trio._core._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('trio._core._generated_instrumentation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_generated_instrumentation.py',
   'PYMODULE'),
  ('trio._core._generated_io_epoll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_generated_io_epoll.py',
   'PYMODULE'),
  ('trio._core._generated_io_kqueue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_generated_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._generated_io_windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_generated_io_windows.py',
   'PYMODULE'),
  ('trio._core._generated_run',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_generated_run.py',
   'PYMODULE'),
  ('trio._core._instrumentation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_instrumentation.py',
   'PYMODULE'),
  ('trio._core._io_common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_io_common.py',
   'PYMODULE'),
  ('trio._core._io_epoll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_io_epoll.py',
   'PYMODULE'),
  ('trio._core._io_kqueue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._io_windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_io_windows.py',
   'PYMODULE'),
  ('trio._core._ki',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_ki.py',
   'PYMODULE'),
  ('trio._core._local',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_local.py',
   'PYMODULE'),
  ('trio._core._mock_clock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_mock_clock.py',
   'PYMODULE'),
  ('trio._core._parking_lot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_parking_lot.py',
   'PYMODULE'),
  ('trio._core._run',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_run.py',
   'PYMODULE'),
  ('trio._core._run_context',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_run_context.py',
   'PYMODULE'),
  ('trio._core._thread_cache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_thread_cache.py',
   'PYMODULE'),
  ('trio._core._traps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_traps.py',
   'PYMODULE'),
  ('trio._core._unbounded_queue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_unbounded_queue.py',
   'PYMODULE'),
  ('trio._core._wakeup_socketpair',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_wakeup_socketpair.py',
   'PYMODULE'),
  ('trio._core._windows_cffi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_core\\_windows_cffi.py',
   'PYMODULE'),
  ('trio._deprecate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_deprecate.py',
   'PYMODULE'),
  ('trio._dtls',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_dtls.py',
   'PYMODULE'),
  ('trio._file_io',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_file_io.py',
   'PYMODULE'),
  ('trio._highlevel_generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_highlevel_generic.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_listeners',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_highlevel_open_tcp_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_stream',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_highlevel_open_tcp_stream.py',
   'PYMODULE'),
  ('trio._highlevel_open_unix_stream',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_highlevel_open_unix_stream.py',
   'PYMODULE'),
  ('trio._highlevel_serve_listeners',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_highlevel_serve_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_socket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_highlevel_socket.py',
   'PYMODULE'),
  ('trio._highlevel_ssl_helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_highlevel_ssl_helpers.py',
   'PYMODULE'),
  ('trio._path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_path.py',
   'PYMODULE'),
  ('trio._signals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_signals.py',
   'PYMODULE'),
  ('trio._socket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_socket.py',
   'PYMODULE'),
  ('trio._ssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_ssl.py',
   'PYMODULE'),
  ('trio._subprocess',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_subprocess.py',
   'PYMODULE'),
  ('trio._subprocess_platform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_subprocess_platform\\__init__.py',
   'PYMODULE'),
  ('trio._subprocess_platform.kqueue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_subprocess_platform\\kqueue.py',
   'PYMODULE'),
  ('trio._subprocess_platform.waitid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_subprocess_platform\\waitid.py',
   'PYMODULE'),
  ('trio._subprocess_platform.windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_subprocess_platform\\windows.py',
   'PYMODULE'),
  ('trio._sync',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_sync.py',
   'PYMODULE'),
  ('trio._threads',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_threads.py',
   'PYMODULE'),
  ('trio._timeouts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_timeouts.py',
   'PYMODULE'),
  ('trio._unix_pipes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_unix_pipes.py',
   'PYMODULE'),
  ('trio._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_util.py',
   'PYMODULE'),
  ('trio._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_version.py',
   'PYMODULE'),
  ('trio._wait_for_object',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_wait_for_object.py',
   'PYMODULE'),
  ('trio._windows_pipes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\_windows_pipes.py',
   'PYMODULE'),
  ('trio.abc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\abc.py',
   'PYMODULE'),
  ('trio.from_thread',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\from_thread.py',
   'PYMODULE'),
  ('trio.lowlevel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\lowlevel.py',
   'PYMODULE'),
  ('trio.socket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\socket.py',
   'PYMODULE'),
  ('trio.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\testing\\__init__.py',
   'PYMODULE'),
  ('trio.testing._check_streams',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\testing\\_check_streams.py',
   'PYMODULE'),
  ('trio.testing._checkpoints',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\testing\\_checkpoints.py',
   'PYMODULE'),
  ('trio.testing._memory_streams',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\testing\\_memory_streams.py',
   'PYMODULE'),
  ('trio.testing._network',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\testing\\_network.py',
   'PYMODULE'),
  ('trio.testing._raises_group',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\testing\\_raises_group.py',
   'PYMODULE'),
  ('trio.testing._sequencer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\testing\\_sequencer.py',
   'PYMODULE'),
  ('trio.testing._trio_test',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\testing\\_trio_test.py',
   'PYMODULE'),
  ('trio.to_thread',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\trio\\to_thread.py',
   'PYMODULE'),
  ('tty', 'C:\\Program Files\\Python312\\Lib\\tty.py', 'PYMODULE'),
  ('typeguard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\__init__.py',
   'PYMODULE'),
  ('typeguard._checkers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_checkers.py',
   'PYMODULE'),
  ('typeguard._config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_config.py',
   'PYMODULE'),
  ('typeguard._decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_decorators.py',
   'PYMODULE'),
  ('typeguard._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_exceptions.py',
   'PYMODULE'),
  ('typeguard._functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_functions.py',
   'PYMODULE'),
  ('typeguard._importhook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_importhook.py',
   'PYMODULE'),
  ('typeguard._memo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_memo.py',
   'PYMODULE'),
  ('typeguard._suppression',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_suppression.py',
   'PYMODULE'),
  ('typeguard._transformer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_transformer.py',
   'PYMODULE'),
  ('typeguard._union_transformer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_union_transformer.py',
   'PYMODULE'),
  ('typeguard._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard\\_utils.py',
   'PYMODULE'),
  ('typing', 'C:\\Program Files\\Python312\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Program Files\\Python312\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Program Files\\Python312\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Program Files\\Python312\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Program Files\\Python312\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Program Files\\Python312\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Program Files\\Python312\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Program Files\\Python312\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Program Files\\Python312\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Program Files\\Python312\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Program Files\\Python312\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Program Files\\Python312\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Program Files\\Python312\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Program Files\\Python312\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Program Files\\Python312\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program Files\\Python312\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Program Files\\Python312\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Program Files\\Python312\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid', 'C:\\Program Files\\Python312\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser',
   'C:\\Program Files\\Python312\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\__init__.py',
   'PYMODULE'),
  ('wheel.cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\util.py',
   'PYMODULE'),
  ('wheel.vendored',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('win32con',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml', 'C:\\Program Files\\Python312\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom',
   'C:\\Program Files\\Python312\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Program Files\\Python312\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Program Files\\Python312\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Program Files\\Python312\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Program Files\\Python312\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Program Files\\Python312\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Program Files\\Python312\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Program Files\\Python312\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Program Files\\Python312\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Program Files\\Python312\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Program Files\\Python312\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Program Files\\Python312\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Program Files\\Python312\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Program Files\\Python312\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Program Files\\Python312\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Program Files\\Python312\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Program Files\\Python312\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc.server',
   'C:\\Program Files\\Python312\\Lib\\xmlrpc\\server.py',
   'PYMODULE'),
  ('yaml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Program Files\\Python312\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile.__main__',
   'C:\\Program Files\\Python312\\Lib\\zipfile\\__main__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Program Files\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Program Files\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'C:\\Program Files\\Python312\\Lib\\zipimport.py', 'PYMODULE'),
  ('zipp',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp._functools',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\zipp\\_functools.py',
   'PYMODULE'),
  ('zipp.compat',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.compat.py313',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\zipp\\compat\\py313.py',
   'PYMODULE'),
  ('zipp.glob',
   'C:\\Program Files\\Python312\\Lib\\site-packages\\zipp\\glob.py',
   'PYMODULE'),
  ('zope', '-', 'PYMODULE'),
  ('zope.interface',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\zope\\interface\\__init__.py',
   'PYMODULE'),
  ('zope.interface._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\zope\\interface\\_compat.py',
   'PYMODULE'),
  ('zope.interface.declarations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\zope\\interface\\declarations.py',
   'PYMODULE'),
  ('zope.interface.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\zope\\interface\\exceptions.py',
   'PYMODULE'),
  ('zope.interface.interface',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\zope\\interface\\interface.py',
   'PYMODULE'),
  ('zope.interface.interfaces',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\zope\\interface\\interfaces.py',
   'PYMODULE'),
  ('zope.interface.ro',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\zope\\interface\\ro.py',
   'PYMODULE'),
  ('zstandard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE')])
