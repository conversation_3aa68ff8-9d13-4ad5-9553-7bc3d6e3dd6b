# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_all
import os
import sys

project_dir = os.getcwd()

# Get the actual site-packages path from the current Python environment
site_packages = next(p for p in sys.path if 'site-packages' in p)
pyfiglet_path = os.path.join(site_packages, 'pyfiglet')

# Collect all aiosqlite data
datas = []
binaries = []
hiddenimports = []
tmp_ret = collect_all('aiosqlite')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]

# Add pyfiglet data
datas.append((pyfiglet_path, 'pyfiglet'))

# Add additional hidden imports
hiddenimports += [
    'sqlite3',
    'asyncio',
    'telegram',
    'telegram.ext',
    'httpx',
    'requests'
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['matplotlib_inline', 'matplotlib'],
    noarchive=False,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='main',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
