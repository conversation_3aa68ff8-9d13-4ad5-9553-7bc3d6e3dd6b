# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_all, collect_submodules, collect_data_files
import os
import sys

project_dir = os.getcwd()

# Get the actual site-packages path from the current Python environment
site_packages = next(p for p in sys.path if 'site-packages' in p)
pyfiglet_path = os.path.join(site_packages, 'pyfiglet')

# Initialize collections
datas = []
binaries = []
hiddenimports = []

# Try to collect aiosqlite data and submodules
try:
    aiosqlite_submodules = collect_submodules('aiosqlite')
    hiddenimports.extend(aiosqlite_submodules)
    print(f"Collected aiosqlite submodules: {aiosqlite_submodules}")
except Exception as e:
    print(f"Failed to collect aiosqlite submodules: {e}")

# Try to collect aiosqlite data files
try:
    aiosqlite_datas = collect_data_files('aiosqlite')
    datas.extend(aiosqlite_datas)
    print(f"Collected aiosqlite data files: {aiosqlite_datas}")
except Exception as e:
    print(f"Failed to collect aiosqlite data files: {e}")

# Collect telegram bot data
telegram_datas, telegram_binaries, telegram_hiddenimports = collect_all('telegram')
datas.extend(telegram_datas)
binaries.extend(telegram_binaries)
hiddenimports.extend(telegram_hiddenimports)

# Collect httpx data
httpx_datas, httpx_binaries, httpx_hiddenimports = collect_all('httpx')
datas.extend(httpx_datas)
binaries.extend(httpx_binaries)
hiddenimports.extend(httpx_hiddenimports)

# Collect python-dotenv data
dotenv_datas, dotenv_binaries, dotenv_hiddenimports = collect_all('dotenv')
datas.extend(dotenv_datas)
binaries.extend(dotenv_binaries)
hiddenimports.extend(dotenv_hiddenimports)

# Add pyfiglet data
datas.append((pyfiglet_path, 'pyfiglet'))

# Add local data files
datas.append(('useragents.json', '.'))
datas.append(('config.py', '.'))

# Collect hwid module if available
try:
    hwid_datas, hwid_binaries, hwid_hiddenimports = collect_all('hwid')
    datas.extend(hwid_datas)
    binaries.extend(hwid_binaries)
    hiddenimports.extend(hwid_hiddenimports)
except:
    pass

# Add comprehensive hidden imports
hiddenimports += [
    # Core Python modules
    'sqlite3',
    'asyncio',
    'threading',
    'datetime',
    'logging',
    'json',
    'hashlib',
    'uuid',
    'pathlib',
    'platform',
    'signal',
    'traceback',
    'html',
    'errno',
    'time',
    're',
    'random',
    'typing',

    # aiosqlite and related - be very explicit
    'aiosqlite',
    'aiosqlite.core',
    'aiosqlite.context',
    'aiosqlite.cursor',
    'aiosqlite.connection',

    # Telegram bot
    'telegram',
    'telegram.ext',
    'telegram.ext._application',
    'telegram.ext._applicationbuilder',
    'telegram.ext._commandhandler',
    'telegram.ext._callbackqueryhandler',
    'telegram.ext._messagehandler',
    'telegram.error',

    # HTTP clients
    'httpx',
    'httpx._transports',
    'httpx._transports.default',
    'requests',
    'aiohttp',
    'urllib3',
    'certifi',

    # Environment and config
    'python-dotenv',
    'dotenv',
    'os',

    # Other dependencies
    'psutil',
    'colorama',
    'tenacity',
    'lxml',
    'lxml.html',
    'PySocks',

    # Local modules
    'config',
    'event_monitor',
    'helper',
    'elitesoftworks',
    'chart_token_manager',
    'token_retrieval',
    'account_token_manager',

    # Additional modules that might be missing
    'pyfiglet',
    'hwid'
]

a = Analysis(
    ['main.py'],
    pathex=[project_dir],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib_inline',
        'matplotlib',
        'pkg_resources',  # Exclude deprecated pkg_resources to avoid warnings
        'setuptools.pkg_resources',
        'IPython',
        'jupyter',
        'notebook',
        'PyQt4',
        'PyQt5',
        'PyQt6',
        'PySide',
        'PySide2',
        'tkinter',
        'Tkinter'
    ],
    noarchive=False,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='main',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
