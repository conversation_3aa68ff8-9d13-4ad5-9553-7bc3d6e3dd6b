#!/usr/bin/env python3
# test_imports.py - Test script to verify all imports work

import sys
print("Python path:")
for p in sys.path:
    print(f"  {p}")

print("\nTesting imports...")

try:
    import aiosqlite
    print(f"✓ aiosqlite imported successfully from: {aiosqlite.__file__}")
    print(f"  aiosqlite version: {getattr(aiosqlite, '__version__', 'unknown')}")
except ImportError as e:
    print(f"✗ aiosqlite import failed: {e}")

try:
    import sqlite3
    print(f"✓ sqlite3 imported successfully")
except ImportError as e:
    print(f"✗ sqlite3 import failed: {e}")

try:
    import telegram
    print(f"✓ telegram imported successfully")
except ImportError as e:
    print(f"✗ telegram import failed: {e}")

try:
    import httpx
    print(f"✓ httpx imported successfully")
except ImportError as e:
    print(f"✗ httpx import failed: {e}")

try:
    import config
    print(f"✓ config imported successfully")
except ImportError as e:
    print(f"✗ config import failed: {e}")

try:
    import event_monitor
    print(f"✓ event_monitor imported successfully")
except ImportError as e:
    print(f"✗ event_monitor import failed: {e}")

print("\nTest completed.")
