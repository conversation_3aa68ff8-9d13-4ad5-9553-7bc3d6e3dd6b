Final working PyInstaller build command:

pyinstaller --onefile --add-data="C:\Users\<USER>\Downloads\شئون_عاملين_2024\xl\venv\Lib\site-packages\aiosqlite;aiosqlite" --add-data="C:\Users\<USER>\Downloads\شئون_عاملين_2024\xl\venv\Lib\site-packages\pyfiglet\fonts;pyfiglet\fonts" --hidden-import=sqlite3 --hidden-import=telegram --hidden-import=httpx --hidden-import=config --hidden-import=event_monitor --hidden-import=helper --hidden-import=elitesoftworks --hidden-import=chart_token_manager --hidden-import=token_retrieval --hidden-import=account_token_manager --exclude-module=matplotlib --exclude-module=matplotlib_inline --exclude-module=IPython --exclude-module=jupyter --exclude-module=PyQt5 --exclude-module=tkinter --add-data="useragents.json;." --add-data="config.py;." main.py

This command successfully builds a working executable that:
1. Includes aiosqlite for database operations
2. Includes pyfiglet fonts for ASCII art display
3. Includes all necessary hidden imports
4. Excludes problematic modules that aren't needed
5. Includes required data files (useragents.json, config.py)
