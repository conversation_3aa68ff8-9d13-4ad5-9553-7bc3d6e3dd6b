# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['test_imports.py'],
    pathex=[],
    binaries=[],
    datas=[('C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite', 'aiosqlite')],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['matplotlib', 'matplotlib_inline', 'IPython', 'jupyter', 'PyQt5', 'tkinter'],
    noarchive=False,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='test_imports',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
