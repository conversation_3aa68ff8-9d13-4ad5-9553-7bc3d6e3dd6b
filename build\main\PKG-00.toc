('E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\build\\main\\main.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\build\\main\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\build\\main\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\build\\main\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\build\\main\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\build\\main\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\build\\main\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('main',
   'E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\main.py',
   'PYSOURCE'),
  ('python312.dll', 'C:\\Program Files\\Python312\\python312.dll', 'BINARY'),
  ('_decimal.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'C:\\Program Files\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Program Files\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program Files\\Python312\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'C:\\Program Files\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'C:\\Program Files\\Python312\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'C:\\Program Files\\Python312\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Program Files\\Python312\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('zope\\interface\\_zope_interface_coptimizations.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\zope\\interface\\_zope_interface_coptimizations.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'C:\\Program Files\\Python312\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp312-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PIL\\_imagingft.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp312-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PIL\\_webp.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PIL\\_imagingtk.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PIL\\_imagingcms.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy\\fft\\_pocketfft_internal.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\yaml\\_yaml.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp312-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PIL\\_imagingmath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_cffi_backend.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp312-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\PIL\\_imaging.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_testcapi.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_testcapi.pyd',
   'EXTENSION'),
  ('_testinternalcapi.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_testinternalcapi.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('zstandard\\_cffi.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\zstandard\\_cffi.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\backend_c.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\zstandard\\backend_c.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_brotli.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_brotli.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\etree.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\_elementpath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\sax.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\objectify.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\diff.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\clean.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\clean.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\builder.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('tornado\\speedups.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\tornado\\speedups.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'C:\\Program Files\\Python312\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files\\Python312\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program Files\\Python312\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Program Files\\Python312\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Program Files\\Python312\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'BINARY'),
  ('python3.dll', 'C:\\Program Files\\Python312\\python3.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Program Files\\Python312\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes312.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pywin32_system32\\pywintypes312.dll',
   'BINARY'),
  ('sqlite3.dll', 'C:\\Program Files\\Python312\\DLLs\\sqlite3.dll', 'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('aiosqlite\\__init__.py',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\__init__.py',
   'DATA'),
  ('aiosqlite\\__pycache__\\__init__.cpython-312.pyc',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('aiosqlite\\__pycache__\\__version__.cpython-312.pyc',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\__pycache__\\__version__.cpython-312.pyc',
   'DATA'),
  ('aiosqlite\\__pycache__\\context.cpython-312.pyc',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\__pycache__\\context.cpython-312.pyc',
   'DATA'),
  ('aiosqlite\\__pycache__\\core.cpython-312.pyc',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\__pycache__\\core.cpython-312.pyc',
   'DATA'),
  ('aiosqlite\\__pycache__\\cursor.cpython-312.pyc',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\__pycache__\\cursor.cpython-312.pyc',
   'DATA'),
  ('aiosqlite\\__version__.py',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\__version__.py',
   'DATA'),
  ('aiosqlite\\context.py',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\context.py',
   'DATA'),
  ('aiosqlite\\core.py',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\core.py',
   'DATA'),
  ('aiosqlite\\cursor.py',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\cursor.py',
   'DATA'),
  ('aiosqlite\\py.typed',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\py.typed',
   'DATA'),
  ('aiosqlite\\tests\\__init__.py',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\tests\\__init__.py',
   'DATA'),
  ('aiosqlite\\tests\\__main__.py',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\tests\\__main__.py',
   'DATA'),
  ('aiosqlite\\tests\\__pycache__\\__init__.cpython-312.pyc',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\tests\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('aiosqlite\\tests\\__pycache__\\__main__.cpython-312.pyc',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\tests\\__pycache__\\__main__.cpython-312.pyc',
   'DATA'),
  ('aiosqlite\\tests\\__pycache__\\helpers.cpython-312.pyc',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\tests\\__pycache__\\helpers.cpython-312.pyc',
   'DATA'),
  ('aiosqlite\\tests\\__pycache__\\perf.cpython-312.pyc',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\tests\\__pycache__\\perf.cpython-312.pyc',
   'DATA'),
  ('aiosqlite\\tests\\__pycache__\\smoke.cpython-312.pyc',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\tests\\__pycache__\\smoke.cpython-312.pyc',
   'DATA'),
  ('aiosqlite\\tests\\helpers.py',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\tests\\helpers.py',
   'DATA'),
  ('aiosqlite\\tests\\perf.py',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\tests\\perf.py',
   'DATA'),
  ('aiosqlite\\tests\\smoke.py',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\aiosqlite\\tests\\smoke.py',
   'DATA'),
  ('config.py',
   'E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\config.py',
   'DATA'),
  ('pyfiglet\\fonts\\1943____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\1943____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\1row.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\1row.flf',
   'DATA'),
  ('pyfiglet\\fonts\\3-d.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\3-d.flf',
   'DATA'),
  ('pyfiglet\\fonts\\3d-ascii.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\3d-ascii.flf',
   'DATA'),
  ('pyfiglet\\fonts\\3d_diagonal.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\3d_diagonal.flf',
   'DATA'),
  ('pyfiglet\\fonts\\3x5.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\3x5.flf',
   'DATA'),
  ('pyfiglet\\fonts\\4max.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\4max.flf',
   'DATA'),
  ('pyfiglet\\fonts\\4x4_offr.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\4x4_offr.flf',
   'DATA'),
  ('pyfiglet\\fonts\\5lineoblique.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\5lineoblique.flf',
   'DATA'),
  ('pyfiglet\\fonts\\5x7.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\5x7.flf',
   'DATA'),
  ('pyfiglet\\fonts\\5x8.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\5x8.flf',
   'DATA'),
  ('pyfiglet\\fonts\\64f1____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\64f1____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\6x10.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\6x10.flf',
   'DATA'),
  ('pyfiglet\\fonts\\6x9.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\6x9.flf',
   'DATA'),
  ('pyfiglet\\fonts\\__init__.py',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\__init__.py',
   'DATA'),
  ('pyfiglet\\fonts\\__pycache__\\__init__.cpython-312.pyc',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('pyfiglet\\fonts\\a_zooloo.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\a_zooloo.flf',
   'DATA'),
  ('pyfiglet\\fonts\\acrobatic.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\acrobatic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\advenger.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\advenger.flf',
   'DATA'),
  ('pyfiglet\\fonts\\alligator.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\alligator.flf',
   'DATA'),
  ('pyfiglet\\fonts\\alligator2.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\alligator2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\alpha.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\alpha.flf',
   'DATA'),
  ('pyfiglet\\fonts\\alphabet.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\alphabet.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_3_line.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_3_line.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_3_liv1.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_3_liv1.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_aaa01.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_aaa01.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_neko.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_neko.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_razor.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_razor.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_razor2.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_razor2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_slash.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_slash.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_slider.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_slider.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_thin.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_thin.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_tubes.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_tubes.flf',
   'DATA'),
  ('pyfiglet\\fonts\\amc_untitled.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\amc_untitled.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ansi_regular.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ansi_regular.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ansi_shadow.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ansi_shadow.flf',
   'DATA'),
  ('pyfiglet\\fonts\\aquaplan.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\aquaplan.flf',
   'DATA'),
  ('pyfiglet\\fonts\\arrows.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\arrows.flf',
   'DATA'),
  ('pyfiglet\\fonts\\asc_____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\asc_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ascii12.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ascii12.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\ascii9.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ascii9.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\ascii___.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ascii___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ascii_new_roman.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ascii_new_roman.flf',
   'DATA'),
  ('pyfiglet\\fonts\\assalt_m.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\assalt_m.flf',
   'DATA'),
  ('pyfiglet\\fonts\\asslt__m.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\asslt__m.flf',
   'DATA'),
  ('pyfiglet\\fonts\\atc_____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\atc_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\atc_gran.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\atc_gran.flf',
   'DATA'),
  ('pyfiglet\\fonts\\avatar.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\avatar.flf',
   'DATA'),
  ('pyfiglet\\fonts\\b1ff.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\b1ff.flf',
   'DATA'),
  ('pyfiglet\\fonts\\b_m__200.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\b_m__200.flf',
   'DATA'),
  ('pyfiglet\\fonts\\banner.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\banner.flf',
   'DATA'),
  ('pyfiglet\\fonts\\banner3-D.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\banner3-D.flf',
   'DATA'),
  ('pyfiglet\\fonts\\banner3.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\banner3.flf',
   'DATA'),
  ('pyfiglet\\fonts\\banner4.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\banner4.flf',
   'DATA'),
  ('pyfiglet\\fonts\\barbwire.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\barbwire.flf',
   'DATA'),
  ('pyfiglet\\fonts\\basic.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\basic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\battle_s.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\battle_s.flf',
   'DATA'),
  ('pyfiglet\\fonts\\battlesh.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\battlesh.flf',
   'DATA'),
  ('pyfiglet\\fonts\\baz__bil.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\baz__bil.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bear.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bear.flf',
   'DATA'),
  ('pyfiglet\\fonts\\beer_pub.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\beer_pub.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bell.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bell.flf',
   'DATA'),
  ('pyfiglet\\fonts\\benjamin.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\benjamin.flf',
   'DATA'),
  ('pyfiglet\\fonts\\big.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\big.flf',
   'DATA'),
  ('pyfiglet\\fonts\\big_money-ne.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\big_money-ne.flf',
   'DATA'),
  ('pyfiglet\\fonts\\big_money-nw.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\big_money-nw.flf',
   'DATA'),
  ('pyfiglet\\fonts\\big_money-se.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\big_money-se.flf',
   'DATA'),
  ('pyfiglet\\fonts\\big_money-sw.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\big_money-sw.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bigascii12.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bigascii12.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\bigascii9.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bigascii9.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\bigchief.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bigchief.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bigfig.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bigfig.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bigmono12.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bigmono12.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\bigmono9.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bigmono9.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\binary.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\binary.flf',
   'DATA'),
  ('pyfiglet\\fonts\\block.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\block.flf',
   'DATA'),
  ('pyfiglet\\fonts\\blocks.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\blocks.flf',
   'DATA'),
  ('pyfiglet\\fonts\\blocky.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\blocky.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bloody.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bloody.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bolger.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bolger.flf',
   'DATA'),
  ('pyfiglet\\fonts\\braced.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\braced.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bright.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bright.flf',
   'DATA'),
  ('pyfiglet\\fonts\\brite.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\brite.flf',
   'DATA'),
  ('pyfiglet\\fonts\\briteb.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\briteb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\britebi.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\britebi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\britei.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\britei.flf',
   'DATA'),
  ('pyfiglet\\fonts\\broadway.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\broadway.flf',
   'DATA'),
  ('pyfiglet\\fonts\\broadway_kb.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\broadway_kb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bubble.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bubble.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bubble__.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bubble__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bubble_b.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bubble_b.flf',
   'DATA'),
  ('pyfiglet\\fonts\\bulbhead.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\bulbhead.flf',
   'DATA'),
  ('pyfiglet\\fonts\\c1______.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\c1______.flf',
   'DATA'),
  ('pyfiglet\\fonts\\c2______.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\c2______.flf',
   'DATA'),
  ('pyfiglet\\fonts\\c_ascii_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\c_ascii_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\c_consen.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\c_consen.flf',
   'DATA'),
  ('pyfiglet\\fonts\\calgphy2.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\calgphy2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\caligraphy.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\caligraphy.flf',
   'DATA'),
  ('pyfiglet\\fonts\\calvin_s.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\calvin_s.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cards.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cards.flf',
   'DATA'),
  ('pyfiglet\\fonts\\catwalk.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\catwalk.flf',
   'DATA'),
  ('pyfiglet\\fonts\\caus_in_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\caus_in_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\char1___.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\char1___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\char2___.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\char2___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\char3___.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\char3___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\char4___.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\char4___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\charact1.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\charact1.flf',
   'DATA'),
  ('pyfiglet\\fonts\\charact2.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\charact2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\charact3.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\charact3.flf',
   'DATA'),
  ('pyfiglet\\fonts\\charact4.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\charact4.flf',
   'DATA'),
  ('pyfiglet\\fonts\\charact5.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\charact5.flf',
   'DATA'),
  ('pyfiglet\\fonts\\charact6.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\charact6.flf',
   'DATA'),
  ('pyfiglet\\fonts\\characte.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\characte.flf',
   'DATA'),
  ('pyfiglet\\fonts\\charset_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\charset_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\chartr.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\chartr.flf',
   'DATA'),
  ('pyfiglet\\fonts\\chartri.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\chartri.flf',
   'DATA'),
  ('pyfiglet\\fonts\\chiseled.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\chiseled.flf',
   'DATA'),
  ('pyfiglet\\fonts\\chunky.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\chunky.flf',
   'DATA'),
  ('pyfiglet\\fonts\\circle.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\circle.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\clb6x10.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clb6x10.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clb8x10.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clb8x10.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clb8x8.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clb8x8.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cli8x8.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cli8x8.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr4x6.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr4x6.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr5x10.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr5x10.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr5x6.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr5x6.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr5x8.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr5x8.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr6x10.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr6x10.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr6x6.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr6x6.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr6x8.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr6x8.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr7x10.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr7x10.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr7x8.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr7x8.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr8x10.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr8x10.flf',
   'DATA'),
  ('pyfiglet\\fonts\\clr8x8.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\clr8x8.flf',
   'DATA'),
  ('pyfiglet\\fonts\\coil_cop.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\coil_cop.flf',
   'DATA'),
  ('pyfiglet\\fonts\\coinstak.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\coinstak.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cola.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cola.flf',
   'DATA'),
  ('pyfiglet\\fonts\\colossal.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\colossal.flf',
   'DATA'),
  ('pyfiglet\\fonts\\com_sen_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\com_sen_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\computer.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\computer.flf',
   'DATA'),
  ('pyfiglet\\fonts\\contessa.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\contessa.flf',
   'DATA'),
  ('pyfiglet\\fonts\\contrast.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\contrast.flf',
   'DATA'),
  ('pyfiglet\\fonts\\convoy__.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\convoy__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cosmic.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cosmic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cosmike.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cosmike.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cour.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cour.flf',
   'DATA'),
  ('pyfiglet\\fonts\\courb.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\courb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\courbi.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\courbi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\couri.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\couri.flf',
   'DATA'),
  ('pyfiglet\\fonts\\crawford.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\crawford.flf',
   'DATA'),
  ('pyfiglet\\fonts\\crawford2.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\crawford2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\crazy.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\crazy.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cricket.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cricket.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cursive.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cursive.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cyberlarge.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cyberlarge.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cybermedium.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cybermedium.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cybersmall.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cybersmall.flf',
   'DATA'),
  ('pyfiglet\\fonts\\cygnet.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\cygnet.flf',
   'DATA'),
  ('pyfiglet\\fonts\\d_dragon.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\d_dragon.flf',
   'DATA'),
  ('pyfiglet\\fonts\\danc4.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\danc4.flf',
   'DATA'),
  ('pyfiglet\\fonts\\dancing_font.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\dancing_font.flf',
   'DATA'),
  ('pyfiglet\\fonts\\dcs_bfmo.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\dcs_bfmo.flf',
   'DATA'),
  ('pyfiglet\\fonts\\decimal.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\decimal.flf',
   'DATA'),
  ('pyfiglet\\fonts\\deep_str.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\deep_str.flf',
   'DATA'),
  ('pyfiglet\\fonts\\def_leppard.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\def_leppard.flf',
   'DATA'),
  ('pyfiglet\\fonts\\defleppard.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\defleppard.flf',
   'DATA'),
  ('pyfiglet\\fonts\\delta_corps_priest_1.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\delta_corps_priest_1.flf',
   'DATA'),
  ('pyfiglet\\fonts\\demo_1__.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\demo_1__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\demo_2__.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\demo_2__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\demo_m__.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\demo_m__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\devilish.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\devilish.flf',
   'DATA'),
  ('pyfiglet\\fonts\\diamond.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\diamond.flf',
   'DATA'),
  ('pyfiglet\\fonts\\diet_cola.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\diet_cola.flf',
   'DATA'),
  ('pyfiglet\\fonts\\digital.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\digital.flf',
   'DATA'),
  ('pyfiglet\\fonts\\doh.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\doh.flf',
   'DATA'),
  ('pyfiglet\\fonts\\doom.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\doom.flf',
   'DATA'),
  ('pyfiglet\\fonts\\dos_rebel.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\dos_rebel.flf',
   'DATA'),
  ('pyfiglet\\fonts\\dotmatrix.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\dotmatrix.flf',
   'DATA'),
  ('pyfiglet\\fonts\\double.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\double.flf',
   'DATA'),
  ('pyfiglet\\fonts\\double_blocky.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\double_blocky.flf',
   'DATA'),
  ('pyfiglet\\fonts\\double_shorts.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\double_shorts.flf',
   'DATA'),
  ('pyfiglet\\fonts\\drpepper.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\drpepper.flf',
   'DATA'),
  ('pyfiglet\\fonts\\druid___.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\druid___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\dwhistled.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\dwhistled.flf',
   'DATA'),
  ('pyfiglet\\fonts\\e__fist_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\e__fist_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ebbs_1__.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ebbs_1__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ebbs_2__.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ebbs_2__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\eca_____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\eca_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\efti_robot.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\efti_robot.flf',
   'DATA'),
  ('pyfiglet\\fonts\\eftichess.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\eftichess.flf',
   'DATA'),
  ('pyfiglet\\fonts\\eftifont.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\eftifont.flf',
   'DATA'),
  ('pyfiglet\\fonts\\eftipiti.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\eftipiti.flf',
   'DATA'),
  ('pyfiglet\\fonts\\eftirobot.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\eftirobot.flf',
   'DATA'),
  ('pyfiglet\\fonts\\eftitalic.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\eftitalic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\eftiwall.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\eftiwall.flf',
   'DATA'),
  ('pyfiglet\\fonts\\eftiwater.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\eftiwater.flf',
   'DATA'),
  ('pyfiglet\\fonts\\electronic.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\electronic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\elite.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\elite.flf',
   'DATA'),
  ('pyfiglet\\fonts\\emboss.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\emboss.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\emboss2.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\emboss2.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\epic.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\epic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\etcrvs__.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\etcrvs__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\f15_____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\f15_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\faces_of.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\faces_of.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fair_mea.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fair_mea.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fairligh.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fairligh.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fantasy_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fantasy_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fbr12___.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fbr12___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fbr1____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fbr1____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fbr2____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fbr2____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fbr_stri.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fbr_stri.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fbr_tilt.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fbr_tilt.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fender.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fender.flf',
   'DATA'),
  ('pyfiglet\\fonts\\filter.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\filter.flf',
   'DATA'),
  ('pyfiglet\\fonts\\finalass.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\finalass.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fire_font-k.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fire_font-k.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fire_font-s.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fire_font-s.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fireing_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fireing_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\flipped.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\flipped.flf',
   'DATA'),
  ('pyfiglet\\fonts\\flower_power.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\flower_power.flf',
   'DATA'),
  ('pyfiglet\\fonts\\flyn_sh.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\flyn_sh.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fourtops.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fourtops.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fp1_____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fp1_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fp2_____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fp2_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fraktur.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fraktur.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fun_face.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fun_face.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fun_faces.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fun_faces.flf',
   'DATA'),
  ('pyfiglet\\fonts\\funky_dr.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\funky_dr.flf',
   'DATA'),
  ('pyfiglet\\fonts\\future.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\future.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\future_1.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\future_1.flf',
   'DATA'),
  ('pyfiglet\\fonts\\future_2.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\future_2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\future_3.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\future_3.flf',
   'DATA'),
  ('pyfiglet\\fonts\\future_4.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\future_4.flf',
   'DATA'),
  ('pyfiglet\\fonts\\future_5.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\future_5.flf',
   'DATA'),
  ('pyfiglet\\fonts\\future_6.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\future_6.flf',
   'DATA'),
  ('pyfiglet\\fonts\\future_7.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\future_7.flf',
   'DATA'),
  ('pyfiglet\\fonts\\future_8.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\future_8.flf',
   'DATA'),
  ('pyfiglet\\fonts\\fuzzy.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\fuzzy.flf',
   'DATA'),
  ('pyfiglet\\fonts\\gauntlet.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\gauntlet.flf',
   'DATA'),
  ('pyfiglet\\fonts\\georgi16.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\georgi16.flf',
   'DATA'),
  ('pyfiglet\\fonts\\georgia11.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\georgia11.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ghost.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ghost.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ghost_bo.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ghost_bo.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ghoulish.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ghoulish.flf',
   'DATA'),
  ('pyfiglet\\fonts\\glenyn.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\glenyn.flf',
   'DATA'),
  ('pyfiglet\\fonts\\goofy.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\goofy.flf',
   'DATA'),
  ('pyfiglet\\fonts\\gothic.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\gothic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\gothic__.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\gothic__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\graceful.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\graceful.flf',
   'DATA'),
  ('pyfiglet\\fonts\\gradient.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\gradient.flf',
   'DATA'),
  ('pyfiglet\\fonts\\graffiti.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\graffiti.flf',
   'DATA'),
  ('pyfiglet\\fonts\\grand_pr.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\grand_pr.flf',
   'DATA'),
  ('pyfiglet\\fonts\\greek.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\greek.flf',
   'DATA'),
  ('pyfiglet\\fonts\\green_be.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\green_be.flf',
   'DATA'),
  ('pyfiglet\\fonts\\hades___.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\hades___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\heart_left.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\heart_left.flf',
   'DATA'),
  ('pyfiglet\\fonts\\heart_right.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\heart_right.flf',
   'DATA'),
  ('pyfiglet\\fonts\\heavy_me.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\heavy_me.flf',
   'DATA'),
  ('pyfiglet\\fonts\\helv.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\helv.flf',
   'DATA'),
  ('pyfiglet\\fonts\\helvb.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\helvb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\helvbi.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\helvbi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\helvi.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\helvi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\henry_3d.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\henry_3d.flf',
   'DATA'),
  ('pyfiglet\\fonts\\heroboti.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\heroboti.flf',
   'DATA'),
  ('pyfiglet\\fonts\\hex.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\hex.flf',
   'DATA'),
  ('pyfiglet\\fonts\\hieroglyphs.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\hieroglyphs.flf',
   'DATA'),
  ('pyfiglet\\fonts\\high_noo.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\high_noo.flf',
   'DATA'),
  ('pyfiglet\\fonts\\hills___.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\hills___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\hollywood.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\hollywood.flf',
   'DATA'),
  ('pyfiglet\\fonts\\home_pak.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\home_pak.flf',
   'DATA'),
  ('pyfiglet\\fonts\\horizontal_left.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\horizontal_left.flf',
   'DATA'),
  ('pyfiglet\\fonts\\horizontal_right.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\horizontal_right.flf',
   'DATA'),
  ('pyfiglet\\fonts\\house_of.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\house_of.flf',
   'DATA'),
  ('pyfiglet\\fonts\\hypa_bal.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\hypa_bal.flf',
   'DATA'),
  ('pyfiglet\\fonts\\hyper___.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\hyper___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\icl-1900.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\icl-1900.flf',
   'DATA'),
  ('pyfiglet\\fonts\\impossible.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\impossible.flf',
   'DATA'),
  ('pyfiglet\\fonts\\inc_raw_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\inc_raw_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\invita.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\invita.flf',
   'DATA'),
  ('pyfiglet\\fonts\\isometric1.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\isometric1.flf',
   'DATA'),
  ('pyfiglet\\fonts\\isometric2.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\isometric2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\isometric3.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\isometric3.flf',
   'DATA'),
  ('pyfiglet\\fonts\\isometric4.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\isometric4.flf',
   'DATA'),
  ('pyfiglet\\fonts\\italic.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\italic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\italics_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\italics_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ivrit.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ivrit.flf',
   'DATA'),
  ('pyfiglet\\fonts\\jacky.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\jacky.flf',
   'DATA'),
  ('pyfiglet\\fonts\\jazmine.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\jazmine.flf',
   'DATA'),
  ('pyfiglet\\fonts\\jerusalem.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\jerusalem.flf',
   'DATA'),
  ('pyfiglet\\fonts\\joust___.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\joust___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\js_block_letters.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\js_block_letters.flf',
   'DATA'),
  ('pyfiglet\\fonts\\js_bracket_letters.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\js_bracket_letters.flf',
   'DATA'),
  ('pyfiglet\\fonts\\js_capital_curves.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\js_capital_curves.flf',
   'DATA'),
  ('pyfiglet\\fonts\\js_cursive.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\js_cursive.flf',
   'DATA'),
  ('pyfiglet\\fonts\\js_stick_letters.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\js_stick_letters.flf',
   'DATA'),
  ('pyfiglet\\fonts\\katakana.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\katakana.flf',
   'DATA'),
  ('pyfiglet\\fonts\\kban.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\kban.flf',
   'DATA'),
  ('pyfiglet\\fonts\\keyboard.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\keyboard.flf',
   'DATA'),
  ('pyfiglet\\fonts\\kgames_i.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\kgames_i.flf',
   'DATA'),
  ('pyfiglet\\fonts\\kik_star.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\kik_star.flf',
   'DATA'),
  ('pyfiglet\\fonts\\knob.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\knob.flf',
   'DATA'),
  ('pyfiglet\\fonts\\konto.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\konto.flf',
   'DATA'),
  ('pyfiglet\\fonts\\konto_slant.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\konto_slant.flf',
   'DATA'),
  ('pyfiglet\\fonts\\krak_out.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\krak_out.flf',
   'DATA'),
  ('pyfiglet\\fonts\\larry3d.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\larry3d.flf',
   'DATA'),
  ('pyfiglet\\fonts\\lazy_jon.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\lazy_jon.flf',
   'DATA'),
  ('pyfiglet\\fonts\\lcd.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\lcd.flf',
   'DATA'),
  ('pyfiglet\\fonts\\lean.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\lean.flf',
   'DATA'),
  ('pyfiglet\\fonts\\letter.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\letter.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\letter_w.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\letter_w.flf',
   'DATA'),
  ('pyfiglet\\fonts\\letters.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\letters.flf',
   'DATA'),
  ('pyfiglet\\fonts\\letterw3.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\letterw3.flf',
   'DATA'),
  ('pyfiglet\\fonts\\lexible_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\lexible_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\lil_devil.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\lil_devil.flf',
   'DATA'),
  ('pyfiglet\\fonts\\line_blocks.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\line_blocks.flf',
   'DATA'),
  ('pyfiglet\\fonts\\linux.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\linux.flf',
   'DATA'),
  ('pyfiglet\\fonts\\lockergnome.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\lockergnome.flf',
   'DATA'),
  ('pyfiglet\\fonts\\lower.flc',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\lower.flc',
   'DATA'),
  ('pyfiglet\\fonts\\mad_nurs.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mad_nurs.flf',
   'DATA'),
  ('pyfiglet\\fonts\\madrid.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\madrid.flf',
   'DATA'),
  ('pyfiglet\\fonts\\magic_ma.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\magic_ma.flf',
   'DATA'),
  ('pyfiglet\\fonts\\marquee.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\marquee.flf',
   'DATA'),
  ('pyfiglet\\fonts\\master_o.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\master_o.flf',
   'DATA'),
  ('pyfiglet\\fonts\\maxfour.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\maxfour.flf',
   'DATA'),
  ('pyfiglet\\fonts\\mayhem_d.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mayhem_d.flf',
   'DATA'),
  ('pyfiglet\\fonts\\mcg_____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mcg_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\merlin1.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\merlin1.flf',
   'DATA'),
  ('pyfiglet\\fonts\\merlin2.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\merlin2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\mig_ally.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mig_ally.flf',
   'DATA'),
  ('pyfiglet\\fonts\\mike.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mike.flf',
   'DATA'),
  ('pyfiglet\\fonts\\mini.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mini.flf',
   'DATA'),
  ('pyfiglet\\fonts\\mirror.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mirror.flf',
   'DATA'),
  ('pyfiglet\\fonts\\mnemonic.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mnemonic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\modern__.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\modern__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\modular.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\modular.flf',
   'DATA'),
  ('pyfiglet\\fonts\\mono12.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mono12.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\mono9.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mono9.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\morse.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\morse.flf',
   'DATA'),
  ('pyfiglet\\fonts\\morse2.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\morse2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\moscow.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\moscow.flf',
   'DATA'),
  ('pyfiglet\\fonts\\mshebrew210.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\mshebrew210.flf',
   'DATA'),
  ('pyfiglet\\fonts\\muzzle.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\muzzle.flf',
   'DATA'),
  ('pyfiglet\\fonts\\nancyj-fancy.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\nancyj-fancy.flf',
   'DATA'),
  ('pyfiglet\\fonts\\nancyj-improved.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\nancyj-improved.flf',
   'DATA'),
  ('pyfiglet\\fonts\\nancyj-underlined.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\nancyj-underlined.flf',
   'DATA'),
  ('pyfiglet\\fonts\\nancyj.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\nancyj.flf',
   'DATA'),
  ('pyfiglet\\fonts\\new_asci.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\new_asci.flf',
   'DATA'),
  ('pyfiglet\\fonts\\nfi1____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\nfi1____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\nipples.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\nipples.flf',
   'DATA'),
  ('pyfiglet\\fonts\\notie_ca.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\notie_ca.flf',
   'DATA'),
  ('pyfiglet\\fonts\\npn_____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\npn_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\nscript.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\nscript.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ntgreek.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ntgreek.flf',
   'DATA'),
  ('pyfiglet\\fonts\\null.flc',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\null.flc',
   'DATA'),
  ('pyfiglet\\fonts\\nvscript.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\nvscript.flf',
   'DATA'),
  ('pyfiglet\\fonts\\o8.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\o8.flf',
   'DATA'),
  ('pyfiglet\\fonts\\octal.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\octal.flf',
   'DATA'),
  ('pyfiglet\\fonts\\odel_lak.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\odel_lak.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ogre.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ogre.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ok_beer_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ok_beer_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\old_banner.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\old_banner.flf',
   'DATA'),
  ('pyfiglet\\fonts\\os2.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\os2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\outrun__.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\outrun__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\p_s_h_m_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\p_s_h_m_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\p_skateb.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\p_skateb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\pacos_pe.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\pacos_pe.flf',
   'DATA'),
  ('pyfiglet\\fonts\\pagga.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\pagga.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\panther_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\panther_.flf',
   'DATA'),
  ("pyfiglet\\fonts\\patorjk's_cheese.flf",
   "C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\patorjk's_cheese.flf",
   'DATA'),
  ('pyfiglet\\fonts\\patorjk-hex.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\patorjk-hex.flf',
   'DATA'),
  ('pyfiglet\\fonts\\pawn_ins.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\pawn_ins.flf',
   'DATA'),
  ('pyfiglet\\fonts\\pawp.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\pawp.flf',
   'DATA'),
  ('pyfiglet\\fonts\\peaks.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\peaks.flf',
   'DATA'),
  ('pyfiglet\\fonts\\pebbles.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\pebbles.flf',
   'DATA'),
  ('pyfiglet\\fonts\\pepper.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\pepper.flf',
   'DATA'),
  ('pyfiglet\\fonts\\phonix__.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\phonix__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\platoon2.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\platoon2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\platoon_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\platoon_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\pod_____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\pod_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\poison.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\poison.flf',
   'DATA'),
  ('pyfiglet\\fonts\\puffy.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\puffy.flf',
   'DATA'),
  ('pyfiglet\\fonts\\puzzle.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\puzzle.flf',
   'DATA'),
  ('pyfiglet\\fonts\\pyramid.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\pyramid.flf',
   'DATA'),
  ('pyfiglet\\fonts\\r2-d2___.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\r2-d2___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rad_____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rad_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rad_phan.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rad_phan.flf',
   'DATA'),
  ('pyfiglet\\fonts\\radical_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\radical_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rainbow_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rainbow_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rally_s2.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rally_s2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rally_sp.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rally_sp.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rammstein.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rammstein.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rampage_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rampage_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rastan__.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rastan__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\raw_recu.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\raw_recu.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rci_____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rci_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rectangles.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rectangles.flf',
   'DATA'),
  ('pyfiglet\\fonts\\red_phoenix.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\red_phoenix.flf',
   'DATA'),
  ('pyfiglet\\fonts\\relief.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\relief.flf',
   'DATA'),
  ('pyfiglet\\fonts\\relief2.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\relief2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rev.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rev.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ripper!_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ripper!_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\road_rai.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\road_rai.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rockbox_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rockbox_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rok_____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rok_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\roman.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\roman.flf',
   'DATA'),
  ('pyfiglet\\fonts\\roman___.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\roman___.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rot13.flc',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rot13.flc',
   'DATA'),
  ('pyfiglet\\fonts\\rot13.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rot13.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rotated.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rotated.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rounded.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rounded.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rowancap.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rowancap.flf',
   'DATA'),
  ('pyfiglet\\fonts\\rozzo.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\rozzo.flf',
   'DATA'),
  ('pyfiglet\\fonts\\runic.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\runic.flf',
   'DATA'),
  ('pyfiglet\\fonts\\runyc.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\runyc.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sans.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sans.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sansb.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sansb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sansbi.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sansbi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sansi.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sansi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\santa_clara.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\santa_clara.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sblood.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sblood.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sbook.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sbook.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sbookb.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sbookb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sbookbi.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sbookbi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sbooki.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sbooki.flf',
   'DATA'),
  ('pyfiglet\\fonts\\script.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\script.flf',
   'DATA'),
  ('pyfiglet\\fonts\\script__.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\script__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\serifcap.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\serifcap.flf',
   'DATA'),
  ('pyfiglet\\fonts\\shadow.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\shadow.flf',
   'DATA'),
  ('pyfiglet\\fonts\\shimrod.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\shimrod.flf',
   'DATA'),
  ('pyfiglet\\fonts\\short.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\short.flf',
   'DATA'),
  ('pyfiglet\\fonts\\skate_ro.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\skate_ro.flf',
   'DATA'),
  ('pyfiglet\\fonts\\skateord.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\skateord.flf',
   'DATA'),
  ('pyfiglet\\fonts\\skateroc.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\skateroc.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sketch_s.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sketch_s.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sl_script.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sl_script.flf',
   'DATA'),
  ('pyfiglet\\fonts\\slant.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\slant.flf',
   'DATA'),
  ('pyfiglet\\fonts\\slant_relief.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\slant_relief.flf',
   'DATA'),
  ('pyfiglet\\fonts\\slide.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\slide.flf',
   'DATA'),
  ('pyfiglet\\fonts\\slscript.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\slscript.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sm______.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sm______.flf',
   'DATA'),
  ('pyfiglet\\fonts\\small.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\small.flf',
   'DATA'),
  ('pyfiglet\\fonts\\small_caps.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\small_caps.flf',
   'DATA'),
  ('pyfiglet\\fonts\\small_poison.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\small_poison.flf',
   'DATA'),
  ('pyfiglet\\fonts\\small_shadow.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\small_shadow.flf',
   'DATA'),
  ('pyfiglet\\fonts\\small_slant.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\small_slant.flf',
   'DATA'),
  ('pyfiglet\\fonts\\smascii12.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smascii12.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\smascii9.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smascii9.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\smblock.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smblock.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\smbraille.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smbraille.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\smisome1.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smisome1.flf',
   'DATA'),
  ('pyfiglet\\fonts\\smkeyboard.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smkeyboard.flf',
   'DATA'),
  ('pyfiglet\\fonts\\smmono12.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smmono12.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\smmono9.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smmono9.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\smscript.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smscript.flf',
   'DATA'),
  ('pyfiglet\\fonts\\smshadow.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smshadow.flf',
   'DATA'),
  ('pyfiglet\\fonts\\smslant.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smslant.flf',
   'DATA'),
  ('pyfiglet\\fonts\\smtengwar.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\smtengwar.flf',
   'DATA'),
  ('pyfiglet\\fonts\\soft.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\soft.flf',
   'DATA'),
  ('pyfiglet\\fonts\\space_op.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\space_op.flf',
   'DATA'),
  ('pyfiglet\\fonts\\spc_demo.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\spc_demo.flf',
   'DATA'),
  ('pyfiglet\\fonts\\speed.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\speed.flf',
   'DATA'),
  ('pyfiglet\\fonts\\spliff.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\spliff.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stacey.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stacey.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stampate.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stampate.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stampatello.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stampatello.flf',
   'DATA'),
  ('pyfiglet\\fonts\\standard.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\standard.flf',
   'DATA'),
  ('pyfiglet\\fonts\\star_strips.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\star_strips.flf',
   'DATA'),
  ('pyfiglet\\fonts\\star_war.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\star_war.flf',
   'DATA'),
  ('pyfiglet\\fonts\\starwars.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\starwars.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stealth_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stealth_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stellar.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stellar.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stencil1.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stencil1.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stencil2.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stencil2.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stforek.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stforek.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stick_letters.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stick_letters.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stop.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stop.flf',
   'DATA'),
  ('pyfiglet\\fonts\\straight.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\straight.flf',
   'DATA'),
  ('pyfiglet\\fonts\\street_s.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\street_s.flf',
   'DATA'),
  ('pyfiglet\\fonts\\stronger_than_all.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\stronger_than_all.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sub-zero.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sub-zero.flf',
   'DATA'),
  ('pyfiglet\\fonts\\subteran.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\subteran.flf',
   'DATA'),
  ('pyfiglet\\fonts\\super_te.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\super_te.flf',
   'DATA'),
  ('pyfiglet\\fonts\\swamp_land.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\swamp_land.flf',
   'DATA'),
  ('pyfiglet\\fonts\\swan.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\swan.flf',
   'DATA'),
  ('pyfiglet\\fonts\\sweet.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\sweet.flf',
   'DATA'),
  ('pyfiglet\\fonts\\t__of_ap.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\t__of_ap.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tanja.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tanja.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tav1____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tav1____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\taxi____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\taxi____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tec1____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tec1____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tec_7000.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tec_7000.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tecrvs__.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tecrvs__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tengwar.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tengwar.flf',
   'DATA'),
  ('pyfiglet\\fonts\\term.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\term.flf',
   'DATA'),
  ('pyfiglet\\fonts\\test1.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\test1.flf',
   'DATA'),
  ('pyfiglet\\fonts\\the_edge.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\the_edge.flf',
   'DATA'),
  ('pyfiglet\\fonts\\thick.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\thick.flf',
   'DATA'),
  ('pyfiglet\\fonts\\thin.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\thin.flf',
   'DATA'),
  ('pyfiglet\\fonts\\this.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\this.flf',
   'DATA'),
  ('pyfiglet\\fonts\\thorned.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\thorned.flf',
   'DATA'),
  ('pyfiglet\\fonts\\threepoint.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\threepoint.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ti_pan__.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ti_pan__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ticks.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ticks.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ticksslant.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ticksslant.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tiles.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tiles.flf',
   'DATA'),
  ('pyfiglet\\fonts\\times.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\times.flf',
   'DATA'),
  ('pyfiglet\\fonts\\timesofl.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\timesofl.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tinker-toy.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tinker-toy.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tomahawk.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tomahawk.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tombstone.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tombstone.flf',
   'DATA'),
  ('pyfiglet\\fonts\\top_duck.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\top_duck.flf',
   'DATA'),
  ('pyfiglet\\fonts\\train.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\train.flf',
   'DATA'),
  ('pyfiglet\\fonts\\trashman.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\trashman.flf',
   'DATA'),
  ('pyfiglet\\fonts\\trek.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\trek.flf',
   'DATA'),
  ('pyfiglet\\fonts\\triad_st.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\triad_st.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ts1_____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ts1_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tsalagi.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tsalagi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tsm_____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tsm_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tsn_base.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tsn_base.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tty.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tty.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ttyb.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ttyb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\tubular.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\tubular.flf',
   'DATA'),
  ('pyfiglet\\fonts\\twin_cob.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\twin_cob.flf',
   'DATA'),
  ('pyfiglet\\fonts\\twisted.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\twisted.flf',
   'DATA'),
  ('pyfiglet\\fonts\\twopoint.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\twopoint.flf',
   'DATA'),
  ('pyfiglet\\fonts\\type_set.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\type_set.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ucf_fan_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ucf_fan_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\ugalympi.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\ugalympi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\unarmed_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\unarmed_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\univers.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\univers.flf',
   'DATA'),
  ('pyfiglet\\fonts\\upper.flc',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\upper.flc',
   'DATA'),
  ('pyfiglet\\fonts\\usa_____.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\usa_____.flf',
   'DATA'),
  ('pyfiglet\\fonts\\usa_pq__.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\usa_pq__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\usaflag.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\usaflag.flf',
   'DATA'),
  ('pyfiglet\\fonts\\utopia.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\utopia.flf',
   'DATA'),
  ('pyfiglet\\fonts\\utopiab.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\utopiab.flf',
   'DATA'),
  ('pyfiglet\\fonts\\utopiabi.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\utopiabi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\utopiai.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\utopiai.flf',
   'DATA'),
  ('pyfiglet\\fonts\\varsity.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\varsity.flf',
   'DATA'),
  ('pyfiglet\\fonts\\vortron_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\vortron_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\war_of_w.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\war_of_w.flf',
   'DATA'),
  ('pyfiglet\\fonts\\wavy.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\wavy.flf',
   'DATA'),
  ('pyfiglet\\fonts\\weird.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\weird.flf',
   'DATA'),
  ('pyfiglet\\fonts\\wet_letter.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\wet_letter.flf',
   'DATA'),
  ('pyfiglet\\fonts\\whimsy.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\whimsy.flf',
   'DATA'),
  ('pyfiglet\\fonts\\wideterm.tlf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\wideterm.tlf',
   'DATA'),
  ('pyfiglet\\fonts\\wow.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\wow.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xbrite.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xbrite.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xbriteb.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xbriteb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xbritebi.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xbritebi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xbritei.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xbritei.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xchartr.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xchartr.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xchartri.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xchartri.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xcour.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xcour.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xcourb.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xcourb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xcourbi.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xcourbi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xcouri.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xcouri.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xhelv.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xhelv.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xhelvb.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xhelvb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xhelvbi.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xhelvbi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xhelvi.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xhelvi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xsans.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xsans.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xsansb.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xsansb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xsansbi.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xsansbi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xsansi.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xsansi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xsbook.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xsbook.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xsbookb.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xsbookb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xsbookbi.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xsbookbi.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xsbooki.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xsbooki.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xtimes.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xtimes.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xtty.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xtty.flf',
   'DATA'),
  ('pyfiglet\\fonts\\xttyb.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\xttyb.flf',
   'DATA'),
  ('pyfiglet\\fonts\\yie-ar__.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\yie-ar__.flf',
   'DATA'),
  ('pyfiglet\\fonts\\yie_ar_k.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\yie_ar_k.flf',
   'DATA'),
  ('pyfiglet\\fonts\\z-pilot_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\z-pilot_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\zig_zag_.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\zig_zag_.flf',
   'DATA'),
  ('pyfiglet\\fonts\\zone7___.flf',
   'C:\\Users\\<USER>\\Downloads\\شئون_عاملين_2024\\xl\\venv\\Lib\\site-packages\\pyfiglet\\fonts\\zone7___.flf',
   'DATA'),
  ('useragents.json',
   'E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\useragents.json',
   'DATA'),
  ('base_library.zip',
   'E:\\Programming\\Python\\webook\\batches_notifications\\webook_batches_notifications_naif\\build\\main\\base_library.zip',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('cryptography-41.0.4.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography-41.0.4.dist-info\\LICENSE',
   'DATA'),
  ('cryptography-41.0.4.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography-41.0.4.dist-info\\top_level.txt',
   'DATA'),
  ('cryptography-41.0.4.dist-info\\LICENSE.BSD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography-41.0.4.dist-info\\LICENSE.BSD',
   'DATA'),
  ('cryptography-41.0.4.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography-41.0.4.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-41.0.4.dist-info\\LICENSE.APACHE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography-41.0.4.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-41.0.4.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography-41.0.4.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-41.0.4.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography-41.0.4.dist-info\\RECORD',
   'DATA'),
  ('cryptography-41.0.4.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography-41.0.4.dist-info\\METADATA',
   'DATA'),
  ('cryptography-41.0.4.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cryptography-41.0.4.dist-info\\WHEEL',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\licenses\\LICENSE',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\top_level.txt',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\RECORD',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\WHEEL',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\INSTALLER',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\METADATA',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\METADATA',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('typeguard-4.3.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\top_level.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('h2-3.2.0.dist-info\\WHEEL',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\h2-3.2.0.dist-info\\WHEEL',
   'DATA'),
  ('h2-3.2.0.dist-info\\METADATA',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\h2-3.2.0.dist-info\\METADATA',
   'DATA'),
  ('typeguard-4.3.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\LICENSE',
   'DATA'),
  ('typeguard-4.3.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\RECORD',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3-3.5.4.dist-info\\top_level.txt',
   'DATA'),
  ('typeguard-4.3.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('typeguard-4.3.0.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\entry_points.txt',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3-3.5.4.dist-info\\RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('h2-3.2.0.dist-info\\INSTALLER',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\h2-3.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'DATA'),
  ('typeguard-4.3.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\WHEEL',
   'DATA'),
  ('h2-3.2.0.dist-info\\RECORD',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\h2-3.2.0.dist-info\\RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3-3.5.4.dist-info\\METADATA',
   'DATA'),
  ('h2-3.2.0.dist-info\\top_level.txt',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\h2-3.2.0.dist-info\\top_level.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('h2-3.2.0.dist-info\\LICENSE',
   'C:\\Program '
   'Files\\Python312\\Lib\\site-packages\\h2-3.2.0.dist-info\\LICENSE',
   'DATA'),
  ('typeguard-4.3.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\METADATA',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3-3.5.4.dist-info\\INSTALLER',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pyreadline3-3.5.4.dist-info\\WHEEL',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
